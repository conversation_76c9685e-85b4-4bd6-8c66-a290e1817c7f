// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Others
import { FA_ENDPOINT } from '../fa';
import axios from '../axios';

export const fetchProjectData = createAsyncThunk(
  'faReducer/fetchProjectData',
  async (projectId) => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/project/id/${projectId}`);
      if (response?.data?.data)
        return {
          ...response?.data?.data?.[0],
          IS_DISABLED: response?.data?.data?.[0]?.status !== 'draft',
        };
      return {};
    } catch (error) {
      // console.log(error);
      return {};
    }
  }
);

export const fetchAllScenarios = createAsyncThunk(
  'faReducer/fetchAllScenarios',
  async (projectId) => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/scenario/project_id/${projectId}`);
      const temp = response?.data?.data || [];
      return temp;
    } catch {
      return [];
    }
  }
);

export const fetchScenarioData = createAsyncThunk(
  'faReducer/fetchScenarioData',
  async (scenarioId) => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/scenario/id/${scenarioId}`);
      if (response?.data?.data) return response?.data?.data?.[0];
      return {};
    } catch (error) {
      // console.log(error);
      return {};
    }
  }
);

const initialState = {
  projectData: {},
  allScenarios: [],
  scenarioData: {},
};

const faReducer = createSlice({
  name: 'faReducer',
  initialState,
  reducers: {
    setProjectData: (state, action) => {
      state.projectData = action.payload;
    },
    setScenarioData: (state, action) => {
      state.scenarioData = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchProjectData.fulfilled, (state, action) => {
      state.projectData = action.payload;
    });
    builder.addCase(fetchAllScenarios.fulfilled, (state, action) => {
      state.allScenarios = action.payload;
    });
    builder.addCase(fetchScenarioData.fulfilled, (state, action) => {
      state.scenarioData = action.payload;
    });
  },
});

export const { setProjectData, setScenarioData } = faReducer.actions;

export default faReducer.reducer;
