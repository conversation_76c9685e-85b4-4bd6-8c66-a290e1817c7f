// Utility functions for FA module display logic

/**
 * Determines display mode for financial data - always uses yearly format.
 *
 * @param {Object} scenarioData - The scenario data containing contract_period_in_month, expected_rfs_date, and project_start_date
 * @returns {Object} Object containing display mode information
 */
export const getDisplayMode = (scenarioData) => {
  // Default to yearly display if no scenario data is provided
  if (!scenarioData) {
    return {
      isMonthlyDisplay: false,
      periodType: 'yearly',
      totalPeriods: 11, // Default to 11 years (Y0-Y10)
      startGapMonths: 0,
    };
  }

  // Get contract period in months
  const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 120;

  // Calculate gap between project start date and RFS date
  let startGapMonths = 0;
  if (scenarioData.project_start_date && scenarioData.expected_rfs_date) {
    const projectStartDate = new Date(scenarioData.project_start_date);
    const rfsDate = new Date(scenarioData.expected_rfs_date);

    // Calculate difference in months
    startGapMonths = (rfsDate.getFullYear() - projectStartDate.getFullYear()) * 12 +
                     (rfsDate.getMonth() - projectStartDate.getMonth());

    // Ensure gap is not negative
    startGapMonths = Math.max(0, startGapMonths);
  }

  // Always use yearly display mode
  const isMonthlyDisplay = false;

  // Calculate total periods to display based on contract duration and gap
  // Always consider the gap between project start and RFS date
  let totalPeriods;
  
  // Calculate based on total time span (gap + contract duration)
  const totalTimeSpan = contractMonths + startGapMonths;
  totalPeriods = Math.ceil(totalTimeSpan / 12);
  
  // For longer contracts (>12 months), ensure minimum of 11 years
  if (contractMonths > 12) {
    totalPeriods = Math.max(totalPeriods, 11);
  }
  
  // Ensure minimum of 1 period
  totalPeriods = Math.max(totalPeriods, 1);

  return {
    isMonthlyDisplay,
    periodType: 'yearly',
    totalPeriods,
    contractMonths,
    startGapMonths,
  };
};

/**
 * Converts yearly data array to monthly data array
 *
 * @param {Array} yearlyArray - Array of yearly values
 * @param {Number} contractMonths - Total contract months
 * @param {Number} startGapMonths - Gap months between project start and RFS date
 * @returns {Array} Array of monthly values
 */
export const convertYearlyToMonthly = (yearlyArray, contractMonths, startGapMonths = 0) => {
  if (!yearlyArray || !yearlyArray.length) {
    return Array(contractMonths + startGapMonths).fill(0);
  }

  // Create a new array with monthly values
  const monthlyArray = [];

  // Add gap months with zero values
  for (let i = 0; i < startGapMonths; i++) {
    monthlyArray.push(0);
  }

  // For each year in the yearly array
  yearlyArray.forEach((yearValue, yearIndex) => {
    // Calculate how many months to include from this year
    const monthsInThisYear = Math.min(
      12, // Maximum 12 months per year
      contractMonths - (yearIndex * 12) // Remaining contract months
    );

    // Skip if no months to include
    if (monthsInThisYear <= 0) return;

    // Calculate monthly value (divide yearly value by 12)
    const monthlyValue = yearValue / 12;

    // Add monthly values to the array
    for (let i = 0; i < monthsInThisYear; i++) {
      monthlyArray.push(monthlyValue);
    }
  });

  // Ensure the array has exactly contractMonths + startGapMonths elements
  const totalMonths = contractMonths + startGapMonths;
  while (monthlyArray.length < totalMonths) {
    monthlyArray.push(0);
  }

  return monthlyArray.slice(0, totalMonths);
};

/**
 * Generates period labels (Y0, Y1, etc.) - always yearly format
 *
 * @param {Boolean} isMonthlyDisplay - Deprecated parameter, always generates yearly labels
 * @param {Number} totalPeriods - Total number of periods to generate labels for
 * @returns {Array} Array of yearly period labels
 */
export const generatePeriodLabels = (isMonthlyDisplay, totalPeriods) => {
  return Array.from({ length: totalPeriods }, (_, i) => `Y${i}`);
};

/**
 * Formats a date array - always uses yearly format
 *
 * @param {Array} dateArray - Array of dates
 * @param {Boolean} isMonthlyDisplay - Deprecated parameter, always uses yearly format
 * @returns {Array} Formatted date array (yearly)
 */
export const formatDateArray = (dateArray, isMonthlyDisplay) => {
  if (!dateArray || !dateArray.length) return [];

  // Always return the original date array for yearly display
  return dateArray;
};
