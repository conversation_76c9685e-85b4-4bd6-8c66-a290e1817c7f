// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import {
  CheckCircle,
  RemoveCircle,
  RadioButtonUnchecked,
  CircleOutlined,
  Delete,
} from '@mui/icons-material';

// Packages
import moment from 'moment';
import * as R from 'ramda';

// Components
import { TablePaginationCustom } from '../../components/Shared/table';
import FilterRoleButtonGroup from '../../components/Shared/FilterRoleButtonGroup';
import { TextInput, DateInput, SearchInput } from '../../components/Shared/CustomInput';
import Layout from '../../layouts/module/payme';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';

// Others
import { getFilteredStaffData } from '../../utils/shared';
import { PAYME_ENDPOINT } from '../../utils/payme';
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { q, year, dataRole } = query;
  const { setParam, replaceParam } = useParamContext();
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);
  const getTableData = () => {
    const temp = getFilteredStaffData(tableData, dataRole, 'user_role');
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };
  const getHeadCellStyle = () => 'bg-payme text-white text-sm text-center';

  const getBodyCellStyle = () => 'text-center whitespace-nowrap px-1 text-xs';

  // Dialog
  const DIALOG_DATA = {
    staff_id: null,
    year: null,
  };
  const [showOptions] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (action !== 'delete') {
          if (Object.values(dialogData).includes(null)) {
            enqueueSnackbar('Please fill in all field.', {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
              },
            });
            return;
          }
        }

        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${PAYME_ENDPOINT}/collect`, {
              user_id: dialogData?.staff_id,
              year: dialogData?.year,
            });
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || error.message || 'An unexpected error occurred';
        enqueueSnackbar(errorMessage, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      }
    }

    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const handleDeleteCollection = async (data) => {
    try {
      const response = await axios.delete(`${PAYME_ENDPOINT}/collect/${data.id}`);
      let statusVariant;
      let message;
      if (response.data.status === 'success') {
        statusVariant = 'success';
        message = response.data.data;
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }
      enqueueSnackbar(message, {
        variant: statusVariant,
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || error.message || 'An unexpected error occurred';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });
    }
    fetchData();
  };

  const getContributionStatusIcon = (status, monthlyContribution, contribution, enabled) => {
    if (!enabled) {
      return (
        <RemoveCircle sx={{ color: '#000000', borderRadius: '4rem', borderColor: '#f56c2a' }} />
      );
    }

    if (status === 'verified') {
      if (contribution < monthlyContribution) {
        return (
          <CircleOutlined sx={{ color: '#f56c2a', borderRadius: '4rem', borderColor: '#f56c2a' }} />
        );
      }
      return <CheckCircle sx={{ color: '#00b300' }} />;
    }
    if (status === 'pending') {
      return (
        <RadioButtonUnchecked
          sx={{ backgroundColor: '#f56c2a', color: '#f56c2a', borderRadius: '4rem' }}
        />
      );
    }
    return <RadioButtonUnchecked />;
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${PAYME_ENDPOINT}/collect/year/${year}`);
      if (response?.data?.data) {
        setTableData(response?.data?.data);
      }
    } catch (error) {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if ([undefined, '']?.includes(year) || [undefined, '']?.includes(dataRole)) {
      replaceParam({ year: moment()?.format('YYYY'), dataRole: 'all' });
      return;
    }
    fetchData();
  }, [year]);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="rounded-[4px] bg-white p-4 dark:bg-gray-600 dark:text-white">
          <div className="my-4 flex flex-col gap-4">
            <div className="flex items-center">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-4">
                  <p className="text-xl font-semibold">Collection</p>
                  <div className="w-[150px]">
                    <DateInput
                      views={['year']}
                      value={year}
                      returnedFormat="YYYY"
                      onChange={(event) => {
                        setParam({ year: event?.target?.value });
                      }}
                    />
                  </div>
                </div>
                <p className="text-sm">List of member with payment status</p>
              </div>
              <div />
            </div>

            <Divider />

            <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
              <FilterRoleButtonGroup />

              <div className="flex items-center justify-center gap-2">
                <SearchInput />
                {tableData.length > 0 && (
                  <ExportExcelButton
                    data={tableData}
                    filename={`collection-${dataRole}-${year}.csv`}
                  />
                )}
              </div>
            </div>

            <div className="mt-4 overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    <td className={getHeadCellStyle()}>No.</td>
                    <td className={getHeadCellStyle()}>Staff Id</td>
                    <td className={getHeadCellStyle()}>Full Name</td>
                    <td className={getHeadCellStyle()}>Monthly Contribution</td>
                    <td className={getHeadCellStyle()}>Others Contribution</td>
                    {[
                      'Jan',
                      'Feb',
                      'Mar',
                      'Apr',
                      'May',
                      'Jun',
                      'Jul',
                      'Aug',
                      'Sep',
                      'Oct',
                      'Nov',
                      'Dec',
                      'Others',
                    ].map((month, index) => (
                      <td className={getHeadCellStyle()} key={index}>
                        {month}
                      </td>
                    ))}
                    <td className={getHeadCellStyle()}>Total</td>
                    <td className={getHeadCellStyle()}>Action</td>
                  </tr>
                </thead>
                <tbody>
                  {getTableData()
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <tr
                        key={i}
                        className="transition duration-150 odd:bg-[#f8f8f8] even:bg-white hover:bg-gray-200"
                      >
                        <td className={getBodyCellStyle()}>{i + 1 + rowsPerPage * page}</td>
                        <td className={twMerge(getBodyCellStyle(), 'text-center')}>
                          {row.user_id.toUpperCase()}
                        </td>
                        <td className={twMerge(getBodyCellStyle(), 'text-left')}>
                          {row.user_full_name}
                        </td>
                        <td className={getBodyCellStyle()}>{row.monthly_contribution}</td>
                        <td className={getBodyCellStyle()}>{row.others_contribution}</td>
                        {[
                          'jan',
                          'feb',
                          'mar',
                          'apr',
                          'may',
                          'jun',
                          'jul',
                          'aug',
                          'sep',
                          'oct',
                          'nov',
                          'dec',
                          'others',
                        ].map((month, index) => (
                          <td key={index} className={getBodyCellStyle()}>
                            <button
                              type="button"
                              className="whitespace-nowrap"
                              disabled={!row[month]?.enable}
                              onClick={() =>
                                handleClickOpenDialog(true, {
                                  id: row.id,
                                  name: row.user_full_name,
                                  month,
                                  monthData: row[month],
                                  monthly_contribution: row.monthly_contribution,
                                  others_contribution: row.others_contribution,
                                })
                              }
                            >
                              {getContributionStatusIcon(
                                row[month]?.payment_status,
                                row.monthly_contribution,
                                row[month]?.amount,
                                row[month]?.enable
                              )}
                            </button>
                          </td>
                        ))}
                        <td className={getBodyCellStyle()}>{row.total}</td>
                        <td className={getBodyCellStyle()}>
                          <Delete
                            color="warning"
                            fontSize="medium"
                            onClick={() => {
                              handleDeleteCollection(row);
                            }}
                            className="hover:cursor-pointer"
                          />
                        </td>
                      </tr>
                    ))}
                  <tr>
                    <td colSpan={16} />
                    <td className={twMerge(getBodyCellStyle(), 'bg-[#d4ebff]')}>Total</td>
                    <td className={twMerge(getBodyCellStyle(), 'bg-[#ffecec]')}>
                      {R.pipe(R.pluck('total'), R.reduce(R.add, 0))(tableData)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <TablePaginationCustom count={tableData.length} />
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-payme text-center text-white">Collection Details</DialogTitle>
        <DialogContent>
          <div
            className={twMerge(
              'flex w-[400px] flex-col gap-2 overflow-x-hidden overflow-y-hidden px-2 py-4',
              (() => (showOptions ? 'h-[300px]' : ''))()
            )}
          >
            {dialogData?.month === 'others' && (
              <>
                <TextInput
                  name="others"
                  value={dialogData?.month?.toUpperCase()}
                  placeholder="Others"
                  onChange={handleDialogDataChange}
                  disabled="true"
                />
                <TextInput
                  name="others_contribution"
                  value={dialogData?.others_contribution}
                  placeholder="Others Contribution (RM)"
                  onChange={handleDialogDataChange}
                  disabled="true"
                />
              </>
            )}
            {dialogData?.month !== 'others' && (
              <>
                <TextInput
                  name="month"
                  value={dialogData?.month?.toUpperCase()}
                  placeholder="Month"
                  onChange={handleDialogDataChange}
                  disabled="true"
                />
                <TextInput
                  name="monthly_contribution"
                  value={dialogData?.monthly_contribution}
                  placeholder="Monthly Contribution (RM)"
                  onChange={handleDialogDataChange}
                  disabled="true"
                />
              </>
            )}

            <TextInput
              name="total_amount"
              value={dialogData?.monthData?.amount}
              placeholder="Total Pay Amount (RM)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="payment_status"
              value={dialogData?.monthData?.payment_status}
              placeholder="Payment Status"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex justify-end gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
