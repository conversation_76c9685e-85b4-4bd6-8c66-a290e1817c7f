// Next, React, Tw
import { useState, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import Image from 'next/image';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import moment from 'moment';
import * as yup from 'yup';

// Components
import {
  TextInput,
  SelectInput,
  SearchInput,
  DateInput,
} from '../../components/Shared/CustomInput';
import { TablePaginationCustom } from '../../components/Shared/table';
import Layout from '../../layouts/module/payme';
import { CustomAvatar } from '../../components/Shared/custom-avatar';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { PAYME_ENDPOINT, getRoleText } from '../../utils/payme';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { q, year } = query;
  const { setParam, replaceParam } = useParamContext();
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();
  const dispatch = useDispatch();
  const { page, rowsPerPage } = useSelector((state) => state.simi);

  const [cardData, setCardData] = useState({});

  // Table
  const [tableData, setTableData] = useState([]);
  const getTableData = () => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };
  const getBodyCellStyle = () => 'text-center whitespace-nowrap px-1 text-xs';

  // Dialog

  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState({});

  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setDialogOpen(true);
  };

  const schema = yup.object().shape({
    user_reference: yup.string().required(),
    user_category: yup.string().required(),
    user_amount: yup.number().required(),
    year: yup.string().required(),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        const formData = new FormData();
        formData.append('user_id', user?.staff_id);
        formData.append('user_reference', payload?.user_reference);
        formData.append('user_amount', Number(payload?.user_amount));
        formData.append('user_category', payload?.user_category);
        formData.append('year', payload?.year);

        switch (action) {
          case 'post':
            response = await axios.post(`${PAYME_ENDPOINT}/payment`, formData);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Successfully added new expense';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        console.log(error);
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const getStatusStyle = (status) => {
    if (status.toLowerCase().includes('pending')) {
      return 'bg-[#fff1aa]';
    }
    if (status.toLowerCase().includes('verified')) {
      return 'bg-[#a9ffb7]';
    }
    if (status.toLowerCase().includes('rejected')) {
      return 'bg-[#ffcdb7]';
    }
    return 'bg-white';
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(
        `${PAYME_ENDPOINT}/dashboard/user_summary/${user?.staff_id}/${year}`
      );
      setCardData(response.data.data[0]);
    } catch {
      setCardData({});
    }
    try {
      const param = `/user_id/${user?.staff_id}`;
      const response2 = await axios.get(`${PAYME_ENDPOINT}/transaction${param}`);
      setTableData(response2.data.data);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if ([undefined, '']?.includes(year)) {
      replaceParam({ year: moment()?.format('YYYY') });
      return;
    }
    fetchData();
  }, [isAdmin, year]);

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div className="rounded-xl bg-white text-black dark:bg-gray-600 dark:text-white">
        <div className="mx-auto  flex w-full flex-col justify-center gap-6 p-4">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-4">
              <p className="text-sm font-bold">Summary</p>
              <div className="w-[150px]">
                <DateInput
                  views={['year']}
                  value={year}
                  returnedFormat="YYYY"
                  onChange={(event) => {
                    setParam({ year: event?.target?.value });
                  }}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-between gap-4 md:flex-row">
            {[
              {
                title: 'Total Team Collection',
                number: cardData?.total_collection,
                customerStyle: 'bg-[#E3FFE7]',
                icon: '/assets/icons/payme - collection.svg',
              },
              {
                title: 'Total Team Expenses',
                number: cardData?.total_expenses,
                customerStyle: 'bg-[#F6E5E5]',
                icon: '/assets/icons/payme - pending.svg',
              },
              {
                title: 'My Total Paid',
                number: cardData?.total_paid,
                customerStyle: 'bg-[#e3f5ff]',
                icon: '/assets/icons/payme - expenses.svg',
              },
              {
                title: 'My Total Unpaid',
                number: cardData?.total_unpaid,
                customerStyle: 'bg-[#e5ecf6]',
                icon: '/assets/icons/payme - verification.svg',
              },
            ]?.map((o, i) => (
              <div
                key={i}
                className={`flex w-full flex-col gap-2 rounded-[4px]  p-4 text-black md:w-1/4 ${o?.customerStyle}`}
              >
                <div>
                  <p className="text-sm font-bold">{o?.title}</p>
                </div>
                <div className="flex w-full items-end justify-between gap-2 text-3xl font-bold">
                  <p>
                    RM &nbsp;
                    <ReactAnimatedNumber
                      value={o?.number}
                      formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                    />
                  </p>
                  <Image src={o.icon} alt="Card Image" width={40} height={40} />
                </div>
              </div>
            ))}
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex">
              <p className="text-sm font-bold">Latest Transaction of Payment</p>
            </div>
            <div className="flex flex-col justify-between gap-2 md:flex-row">
              <SearchInput />
              <button
                type="button"
                className="rounded-[4px] border border-[#e5e7eb] bg-[#ceff8f] px-2 py-1 text-xs font-semibold text-black"
                onClick={() => handleClickOpenDialog(false)}
              >
                Add Payment
              </button>
            </div>
            <div className="my-4 overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    {['No.', 'Name', 'Status', 'Role', 'Amount', 'Category', 'Last Updated'].map(
                      (label, i) => (
                        <td key={i} className="bg-payme text-sm text-white">
                          <p className="text-center">{label}</p>
                        </td>
                      )
                    )}
                  </tr>
                </thead>
                <tbody>
                  {getTableData()
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <tr
                        key={i}
                        className="transition duration-150 odd:bg-[#f8f8f8] even:bg-white hover:bg-gray-200"
                      >
                        <td className={getBodyCellStyle()}>{i + 1 + rowsPerPage * page}</td>
                        <td className={twMerge(getBodyCellStyle(), '')}>
                          <div className="flex items-center gap-4">
                            <CustomAvatar name={row?.user_full_name} className="h-6 w-6" />
                            <p className="whitespace-nowrap">{row.user_full_name}</p>
                          </div>
                        </td>
                        <td className={twMerge(getBodyCellStyle(), getStatusStyle(row.status))}>
                          {toUpperCaseFirstLetter(row.status)}
                        </td>
                        <td className={getBodyCellStyle()}>{getRoleText(row.user_role)}</td>
                        <td className={getBodyCellStyle()}>{row.transaction_amount}</td>
                        <td className={getBodyCellStyle()}>
                          {toUpperCaseFirstLetter(row.payment_method)}
                        </td>
                        <td className={getBodyCellStyle()}>
                          {moment(row.last_updated).format('YYYY-MM-DD')}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
            <TablePaginationCustom count={getTableData()?.length} />
          </div>
        </div>
        <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)} maxWidth="lg">
          <DialogTitle className="bg-payme text-center text-white">Add New Payment</DialogTitle>
          <DialogContent>
            <div className="flex flex-col gap-4 p-2 md:flex-row md:gap-6 md:p-4">
              <div className="flex w-full flex-col gap-4 rounded-lg bg-gray-50 p-4 md:w-[400px] md:p-6">
                <div className="text-center">
                  <Image
                    src="/assets/logo/payme.png"
                    alt="Payment QR Code"
                    width={500}
                    height={500}
                    className="mx-auto w-full max-w-[300px] md:max-w-[500px]"
                  />
                </div>

                <div className="space-y-4">
                  <div className="rounded-md bg-white p-4 shadow-sm">
                    <h3 className="mb-2 text-sm font-semibold text-gray-700 md:text-base">
                      Bank Details
                    </h3>
                    <div className="space-y-1 text-xs text-gray-600 md:text-sm">
                      <p>Bank Name: Bank Islam</p>
                      <p>Account: **************</p>
                      <p>Name: The Mavericks Squad</p>
                    </div>
                  </div>

                  <div className="rounded-md bg-white p-4 shadow-sm">
                    <h3 className="mb-2 text-sm font-semibold text-gray-700 md:text-base">
                      Payment Instructions
                    </h3>
                    <ol className="ml-4 list-decimal space-y-1 text-xs text-gray-600 md:text-sm">
                      <li>Scan QR code or use bank details above</li>
                      <li>Enter the exact amount as specified</li>
                      <li>Use your (Staff ID - Payme) as reference</li>
                    </ol>
                  </div>
                </div>
              </div>
              <div className="flex w-full flex-col gap-4 md:w-[400px]">
                <div className="rounded-md bg-blue-50 p-4">
                  <h3 className="mb-2 text-sm font-semibold text-blue-700 md:text-base">Note</h3>
                  <p className="text-xs text-blue-600 md:text-sm">
                    Please ensure all details are correct before submitting. Payment verification
                    may take 1-2 working days.
                  </p>
                </div>
                <TextInput
                  name="user_id"
                  value={user?.staff_id}
                  placeholder="Staff ID"
                  onChange={handleDialogDataChange}
                  label="Staff ID"
                  disabled
                />
                <div className="flex flex-col gap-1">
                  <TextInput
                    name="user_reference"
                    value={dialogData?.user_reference}
                    placeholder="User Reference"
                    onChange={handleDialogDataChange}
                    label="Reference Number"
                  />
                  <p className="text-xs italic text-gray-500">
                    ***Please use transaction reference and remarks
                  </p>
                </div>
                <TextInput
                  name="user_amount"
                  value={dialogData?.user_amount}
                  placeholder="Amount"
                  onChange={handleDialogDataChange}
                  label="Payment Amount (RM)"
                />
                <SelectInput
                  name="user_category"
                  value={dialogData?.user_category}
                  placeholder="Category"
                  options={['monthly', 'other']}
                  onChange={handleDialogDataChange}
                  label="Payment Category"
                />
                <DateInput
                  name="year"
                  value={dialogData?.year}
                  placeholder="Payment Year"
                  views={['year']}
                  returnedFormat="YYYY"
                  onChange={handleDialogDataChange}
                />
              </div>
            </div>
          </DialogContent>
          <DialogActions className="bg-gray-50">
            <div className="flex w-full justify-between gap-2 p-2">
              <button
                type="button"
                onClick={() => handleDialogClose(false)}
                className="rounded-md px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => handleDialogClose('post')}
                className="bg-payme rounded-md px-4 py-1.5 text-sm text-white hover:bg-opacity-90"
              >
                Submit Payment
              </button>
            </div>
          </DialogActions>
        </Dialog>
      </div>
    </div>
  );
}
