// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { MoreVert, CloudUpload, Delete } from '@mui/icons-material';

// Packages
import moment from 'moment';
import * as yup from 'yup';

// Components
import { TablePaginationCustom } from '../../components/Shared/table';
import Layout from '../../layouts/module/payme';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import {
  TextInput,
  TextAreaInput,
  DateInput,
  SearchInput,
} from '../../components/Shared/CustomInput';

// Others
import { PAYME_ENDPOINT } from '../../utils/payme';
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const { query, replace, asPath } = useRouter();
  const { q, year } = query;
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { setParam } = useParamContext();
  const dispatch = useDispatch();

  const fileInputRef = useRef(null);

  // Table
  const [tableData, setTableData] = useState([]);
  const getTableData = () => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };
  const getBodyCellStyle = () => 'text-center whitespace-nowrap px-1 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const text = 'Please provide';
  const schema = yup.object({
    title: yup.string().required(`${text} title`),
    description: yup.string().required(`${text} description`),
    amount: yup
      .number()
      .required(`${text} amount`)
      .transform((value) => Number(value)),
    user_id: yup.string().required(`${text} user_id`).default(user?.staff_id),
    remarks: yup.string(),
    file_name: yup.string().required(`${text} attachment`),
    years: yup.string().required(`${text} year`).default(year),
  });

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
        delete payload?.attachment;
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${PAYME_ENDPOINT}/expense`, payload);
            break;
          case 'put':
            response = await axios.put(
              `${PAYME_ENDPOINT}/expense/all/${dialogData.expense_id}`,
              payload
            );
            break;
          case 'delete':
            response = await axios.delete(`${PAYME_ENDPOINT}/expense/${dialogData.expense_id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Successfully added new expense';
          if (action === 'post') {
            await handleUploadAttachment(response?.data?.data?.expense_id);
          } else if (action === 'delete') {
            await handleDeleteAttachment(dialogData.expense_id);
          }
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        // console.log(error);
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  const handleUploadAttachment = async (expenseId) => {
    try {
      await axios.post(
        `${PAYME_ENDPOINT}/file/upload/${expenseId}`,
        { file: dialogData?.attachment },
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
    } catch (error) {
      // console.log(error);
    }
  };

  const downloadAttachment = async (expenseId, fileName) => {
    try {
      const response = await axios.get(`${PAYME_ENDPOINT}/file/download/${expenseId}`, {
        responseType: 'blob',
      });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(response.data);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } catch (error) {
      // console.log(error);
    }
  };

  const handleDeleteAttachment = async (expenseId) => {
    try {
      const response = await axios.delete(`${PAYME_ENDPOINT}/file/${expenseId}`);
      let statusVariant;
      let message;
      if (response.data.status === 'success') {
        statusVariant = 'success';
        message = response.data.data;
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }

      enqueueSnackbar(message, {
        variant: statusVariant,
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });
    } catch (error) {
      // console.log(error);
    }
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${PAYME_ENDPOINT}/expense/years/${year}`);
      if (response.data.status === 'success') {
        setTableData(response.data.data);
      }
    } catch (error) {
      // console.log(error);
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if ([undefined, '']?.includes(year)) {
      replace(`${asPath}?year=${moment()?.format('YYYY')}`);
      return;
    }
    fetchData();
  }, [year]);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="mx-auto rounded-xl bg-white p-4 text-black dark:bg-gray-600 dark:text-white">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
              <div className="flex items-center">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-4">
                    <p className="text-xl font-semibold">Expenses Usage</p>
                    <div className="w-[150px]">
                      <DateInput
                        views={['year']}
                        value={year}
                        returnedFormat="YYYY"
                        onChange={(event) => {
                          setParam({ year: event?.target?.value });
                        }}
                      />
                    </div>
                  </div>

                  <p className="text-sm">List of expenses</p>
                </div>
                <div />
              </div>
              <div className="flex items-center justify-center gap-2">
                <SearchInput />
                <button
                  type="button"
                  className="rounded-[4px] border border-[#e5e7eb] bg-[#ceff8f] px-2 py-1 text-xs font-semibold text-black"
                  onClick={() => handleClickOpenDialog(false)}
                >
                  Add Expense
                </button>
                {tableData.length > 0 && (
                  <ExportExcelButton data={tableData} filename={`expenses-${year}.csv`} />
                )}
              </div>
            </div>

            <div className="mt-4 overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    {[
                      'No.',
                      'Title',
                      'Description',
                      'Amount',
                      'Created Date',
                      'Created By',
                      'Attachment',
                      'Remarks',
                      '',
                    ].map((label, i) => (
                      <td
                        key={i}
                        className="bg-payme whitespace-nowrap px-2 text-center text-sm text-white"
                      >
                        {label}
                      </td>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {getTableData()
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <tr
                        key={i}
                        className="transition duration-150 odd:bg-[#f8f8f8] even:bg-white hover:bg-gray-200"
                      >
                        <td className={getBodyCellStyle()}>{i + 1 + rowsPerPage * page}</td>
                        <td className={getBodyCellStyle()}>{row?.title}</td>
                        <td className={getBodyCellStyle()}>{row?.description}</td>
                        <td className={getBodyCellStyle()}>{row?.amount}</td>
                        <td className={getBodyCellStyle()}>
                          {moment(row?.created_date).format('YYYY-MM-DD HH-MM')}
                        </td>
                        <td className={getBodyCellStyle()}>{row?.created_by?.toUpperCase()}</td>
                        <td className={getBodyCellStyle()}>
                          <div className="flex justify-center">
                            <button
                              type="button"
                              className="rounded-lg bg-[#ffefb7] p-2 font-semibold text-[#783b03]"
                              onClick={() => downloadAttachment(row?.expense_id, row?.file_name)}
                            >
                              Download
                            </button>
                          </div>
                        </td>
                        <td className={getBodyCellStyle()}>{row?.remarks}</td>
                        <td>
                          <MoreVert
                            onClick={() => handleClickOpenDialog(true, row)}
                            className="hover:cursor-pointer"
                          />
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
            <TablePaginationCustom count={tableData.length} />
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-payme text-center text-white">Add New Expense</DialogTitle>
        <DialogContent>
          <div className="flex w-full flex-col gap-4 px-2 py-4  md:w-[400px]">
            <TextInput
              name="title"
              value={dialogData?.title}
              placeholder="Title"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="description"
              value={dialogData?.description}
              placeholder="Description"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="amount"
              value={dialogData?.amount}
              placeholder="Amount"
              onChange={handleDialogDataChange}
            />
            <TextAreaInput
              name="remarks"
              value={dialogData?.remarks}
              rows="5"
              placeholder="Remarks"
              onChange={handleDialogDataChange}
            />
            {dialogData?.file_name === undefined && (
              <button type="button" onClick={() => fileInputRef.current.click()}>
                <div className="flex h-full w-full items-center justify-center rounded-xl border-2 border-dashed border-blue-600 bg-blue-600 bg-opacity-20 md:cursor-pointer">
                  <div className="font-Poppins flex flex-col items-center justify-center p-4">
                    <CloudUpload className="h-[70px] w-full" />
                    <p className="whitespace-nowrap text-center leading-normal tracking-normal">
                      Upload Expense Proof
                    </p>
                    <div className="text-16 whitespace-nowrap text-center font-normal leading-normal tracking-normal">
                      max. 10MB
                    </div>
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      onChange={(event) =>
                        setDialogData((prevValues) => ({
                          ...prevValues,
                          attachment: event.target.files[0],
                          file_name: event.target.files[0]?.name,
                        }))
                      }
                    />
                  </div>
                </div>
              </button>
            )}
            {dialogData?.attachment !== undefined && (
              <div className="flex w-full flex-col gap-2">
                <p className="w-full text-center font-semibold">Attachment</p>
                <div className="flex justify-between px-4">
                  <p className="flex grow">{dialogData?.attachment?.name}</p>
                  <button
                    type="button"
                    onClick={() =>
                      setDialogData((prevValues) => ({
                        ...prevValues,
                        attachment: undefined,
                        file_name: undefined,
                      }))
                    }
                  >
                    <Delete className="text-red-500" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div className="flex">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-[#ff6d6d] p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-payme p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-payme p-2 text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
