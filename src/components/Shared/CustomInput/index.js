// Next, React, Tw
import { twMerge } from 'tailwind-merge';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import ReactDOM from 'react-dom';

// Mui
import { Switch, Radio, Popover } from '@mui/material';
import { CalendarMonth, Sort } from '@mui/icons-material';
import { DatePicker, DateTimePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';

// Packages
import PropTypes from 'prop-types';

// Others
import { useParamContext } from '../../../utils/auth/ParamProvider';
import {
  getColorCode,
  getModuleFromPath,
  checkAndReplaceStringWithHyphen,
} from '../../../utils/shared';

// ----------------------------------------------------------------------

const getValue = (temp) => {
  if (typeof temp === 'number') return Number(temp);
  const temp2 = checkAndReplaceStringWithHyphen(temp)?.toUpperCase();
  if (temp2 === '000000000000000000000000') return '-';
  return temp2;
};

const customClass = (() =>
  twMerge(
    `peer h-full w-full rounded-md border border-gray-300 bg-transparent px-3 pt-2 pb-1 text-xs outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-gray-300 focus:border-1 focus:border-gray-300 focus:outline-0 disabled:border-1 disabled:border-t-transparent disabled:outline-0`
  ))();

// ----------------------------------------------------------------------

export const TextInput = ({
  type = 'string',
  name,
  value,
  placeholder = '',
  onChange,
  onBlur,
  disabled = false,
  showRedAsteric = true,
}) => (
  <div className="relative h-8 w-full min-w-[150px] rounded-md bg-white text-black dark:bg-gray-800 dark:text-white">
    {!disabled && (
      <input
        type={type}
        className={twMerge(
          customClass,
          `${![undefined, null, '']?.includes(value) ? 'border-t-transparent' : ''}`,
          `${placeholder !== '' ? 'focus:border-t-transparent' : ''}`
        )}
        name={name}
        value={value}
        onFocus={() => {
          if (value !== 0) return;
          onChange({
            target: {
              name,
              value: '',
            },
          });
        }}
        onChange={(event) =>
          onChange({
            target: {
              name,
              value: event.target.value,
            },
          })
        }
        onBlur={(event) => {
          const temp = {
            target: {
              name,
              value: type === 'number' ? Number(event.target.value) : event.target.value,
            },
          };
          onChange(temp);
          if (onBlur) onBlur(temp);
        }}
        placeholder=" "
        autoComplete="off"
      />
    )}
    {disabled && (
      <p
        className={twMerge(
          customClass,
          `${![undefined, null, '']?.includes(value) ? 'border-t-transparent' : ''}`
        )}
      >
        {getValue(value)}
      </p>
    )}
    <p
      className={`before:content[' '] after:content[' '] peer-focus:before:border-t-1 peer-focus:after:border-t-1 peer-focus:after:border-r-1 peer-focus:before:border-l-1 pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none overflow-hidden text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-xs peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-gray-500 peer-focus:before:border-gray-300 peer-focus:after:border-gray-300`}
    >
      {placeholder}
      {showRedAsteric && placeholder !== '' && (
        <span className="font-semibold text-red-500">&nbsp;*</span>
      )}
    </p>
  </div>
);

TextInput.propTypes = {
  type: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
};

// ----------------------------------------------------------------------

export const SearchInput = ({ onSortClick, sortActive, placeholder = '', className = '' }) => {
  // Standard
  const { query } = useRouter();
  const { q } = query;
  const { replaceParam } = useParamContext();

  const hasValue = q && q.length > 0;

  return (
    <div className={twMerge("relative", className)}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          className="block w-full pl-10 pr-12 py-2.5 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-colors placeholder-gray-500 dark:placeholder-gray-400"
          onChange={(event) => replaceParam({ q: event.target.value })}
          placeholder={placeholder || "Search..."}
          autoComplete="off"
          value={q || ''}
        />
        {hasValue && (
          <button
            type="button"
            onClick={() => replaceParam({ q: '' })}
            className="absolute inset-y-0 right-8 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
        {onSortClick && (
          <button
            type="button"
            onClick={onSortClick}
            className={`absolute inset-y-0 right-0 flex items-center pr-3 transition-colors ${
              sortActive
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
            }`}
          >
            <Sort className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};

SearchInput.propTypes = {
  onSortClick: PropTypes.func,
  sortActive: PropTypes.bool,
  placeholder: PropTypes.string,
  className: PropTypes.string,
};

// ----------------------------------------------------------------------

export const DateInput = ({
  name,
  value,
  views = ['year', 'month', 'day'],
  placeholder = '',
  returnedFormat,
  onChange,
  disabled = false,
  showRedAsteric = true,
}) => {
  // Standard
  const { asPath } = useRouter();
  const module = getModuleFromPath(asPath);

  // Others
  const getDayjsValue = () =>
    returnedFormat !== 'unix'
      ? dayjs(value, returnedFormat)
      : dayjs.unix(value !== 0 ? value : undefined);

  const getFormat = () => {
    if (views?.[views?.length - 1] === 'year') {
      return 'YYYY';
    }
    if (views?.[views?.length - 1] === 'month') {
      return 'MM/YYYY';
    }
    return 'DD/MM/YYYY';
  };

  return (
    <div className="relative h-8 w-full min-w-[150px] rounded-md bg-white text-black dark:shadow-[0px_-3px_5px_rgba(255,255,255,1.0)]">
      <div
        className={twMerge(customClass, `${placeholder !== '' ? 'border-t-transparent' : ''} cursor-pointer`)}
        onClick={(e) => {
          // Find the input within the DatePicker and trigger a click on it
          const input = e.currentTarget.querySelector('input');
          if (input) input.click();
        }}
      >
        <DatePicker
          key={value}
          views={views}
          value={getDayjsValue()}
          disabled={disabled}
          onChange={(date) => {
            if (returnedFormat === 'unix') {
              onChange({ target: { name, value: dayjs(date).startOf('day').unix() } });
              return;
            }
            onChange({ target: { name, value: dayjs(date).format(returnedFormat) } });
          }}
          format={getFormat()}
          sx={{
            input: {
              '&.Mui-disabled': {
                WebkitTextFillColor: '#000',
              },
              cursor: 'pointer',
            },
            '& .MuiInputBase-root': {
              cursor: 'pointer',
              width: '100%',
            }
          }}
          slotProps={{
            textField: {
              size: 'small',
              variant: 'standard',
              disabled,
              InputProps: {
                style: {
                  fontSize: '10px',
                  borderRadius: '0.5rem',
                  outline: 'none',
                  border: 'none',
                  background: 'white',
                  color: 'black',
                  cursor: 'pointer'
                },
                disableUnderline: true,
              },
            },
          }}
          slots={{
            openPickerIcon: () => (
              <CalendarMonth
                sx={{
                  width: '20px',
                  height: '20px',
                  color: getColorCode(module),
                  cursor: 'pointer',
                }}
              />
            ),
          }}
          className="w-full"
        />
      </div>
      {placeholder !== '' && (
        <p
          className={`before:content[' '] after:content[' '] peer-focus:before:border-t-1 peer-focus:after:border-t-1 peer-focus:after:border-r-1 peer-focus:before:border-l-1 pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none overflow-hidden text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-xs peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-gray-500 peer-focus:before:border-gray-300 peer-focus:after:border-gray-300`}
        >
          {placeholder}
          {showRedAsteric && <span className="font-semibold text-red-500">&nbsp;*</span>}
        </p>
      )}
    </div>
  );
};

DateInput.propTypes = {
  name: PropTypes.string,
  value: PropTypes.string,
  views: PropTypes.array,
  placeholder: PropTypes.string,
  returnedFormat: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
};

// ----------------------------------------------------------------------

export const TimeInput = ({
  name,
  value,
  views = ['year', 'day', 'hours'],
  placeholder = '',
  returnedFormat,
  onChange,
  disabled = false,
  showRedAsteric = true,
}) => {
  // Standard
  const { asPath } = useRouter();
  const module = getModuleFromPath(asPath);

  // Others

  const getDayjsValue = () => dayjs.unix(value !== 0 ? value : undefined);

  return (
    <div className="relative h-8 w-full min-w-[150px] rounded-md bg-white text-black dark:shadow-[0px_-3px_5px_rgba(255,255,255,1.0)]">
      <div className={twMerge(customClass, `${placeholder !== '' ? 'border-t-transparent' : ''}`)}>
        <DateTimePicker
          value={getDayjsValue()}
          format="DD/MM/YYYY, hh:mm A"
          views={views}
          disabled={disabled}
          onChange={(date) => {
            onChange({ target: { name, value: dayjs(date).unix() } });
          }}
          sx={{
            input: {
              '&.Mui-disabled': {
                WebkitTextFillColor: '#000',
              },
            },
          }}
          slotProps={{
            textField: {
              size: 'small',
              variant: 'standard',
              disabled,
              InputProps: {
                style: {
                  fontSize: '10px',
                  borderRadius: '0.5rem',
                  outline: 'none',
                  border: 'none',
                  background: 'white',
                  color: 'black',
                },
                disableUnderline: true,
              },
            },
          }}
          slots={{
            openPickerIcon: () => (
              <div className="absolute right-0 top-0 bottom-0 flex items-center justify-center w-10 cursor-pointer">
                <CalendarMonth
                  sx={{
                    width: '20px',
                    height: '20px',
                    color: getColorCode(module),
                  }}
                />
              </div>
            ),
          }}
          className="w-full"
        />
      </div>
      {placeholder !== '' && (
        <p
          className={`before:content[' '] after:content[' '] peer-focus:before:border-t-1 peer-focus:after:border-t-1 peer-focus:after:border-r-1 peer-focus:before:border-l-1 pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none overflow-hidden text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-xs peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-gray-500 peer-focus:before:border-gray-300 peer-focus:after:border-gray-300`}
        >
          {placeholder}
          {showRedAsteric && <span className="font-semibold text-red-500">&nbsp;*</span>}
        </p>
      )}
    </div>
  );
};

TimeInput.propTypes = {
  name: PropTypes.string,
  value: PropTypes.string,
  views: PropTypes.array,
  placeholder: PropTypes.string,
  returnedFormat: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
};

// ----------------------------------------------------------------------

export const AutoCompleteTextInput = ({
  name,
  value,
  placeholder = '',
  options = [],
  onChange,
  onBlur,
  optionMustBeSelected = false,
  disabled = false,
  showRedAsteric = true,
  className,
  hideExternalLabel = false,
}) => {
  const [inputText, setInputText] = useState('');
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef(null);
  const [blurTimeout, setBlurTimeout] = useState(null);
  const [isBrowser, setIsBrowser] = useState(false);

  useEffect(() => {
    setIsBrowser(true);
  }, []);

  // Sync inputText with the value prop (selected ID)
  useEffect(() => {
    if (!options || options.length === 0) {
      if (!value) setInputText('');
      return;
    }
    const selectedOption = value ? options.find(opt => opt.value === value) : null;
    setInputText(selectedOption ? selectedOption.label : '');

    // If we have a value, hide suggestions
    if (value) {
      setShowSuggestions(false);
    }
  }, [value, options]);

  // Calculate position for dropdown when suggestions change or become visible
  useEffect(() => {
    if (showSuggestions && filteredSuggestions.length > 0 && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 5,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  }, [filteredSuggestions, showSuggestions]);

  const convertedOptions = () =>
    options?.map((o) => {
      if (Object.prototype.toString.call(o) === '[object Object]') return o;
      return { value: o, label: String(o)?.toUpperCase() };
    });

  const handleInputChange = (event) => {
    const currentInputText = event.target.value;
    setInputText(currentInputText);

    if (currentInputText.length === 0) {
      setFilteredSuggestions([]);
      setShowSuggestions(false);
      if (onChange) {
        onChange('');
      }
    } else {
      const filtered = convertedOptions()?.filter((o) =>
        o?.label?.toLowerCase().includes(currentInputText.toLowerCase())
      );
      setFilteredSuggestions(filtered);
      setShowSuggestions(true);
    }
  };

  const handleInputFocus = () => {
    // Only show suggestions on focus if we're typing something
    if (inputText && inputText.length > 0) {
      const filtered = convertedOptions()?.filter((o) =>
        o?.label?.toLowerCase().includes(inputText.toLowerCase())
      );
      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
    }
  };

  const handleClickOutside = (event) => {
    if (inputRef.current && !inputRef.current.contains(event.target)) {
      setShowSuggestions(false);
    }
  };

  // Add click outside listener
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleBlurEvent = (event) => {
    const timeout = setTimeout(() => {
      // Don't hide suggestions immediately to allow for clicking on them
      if (optionMustBeSelected) {
        const currentOption = options.find(opt => opt.label.toLowerCase() === inputText.toLowerCase());
        if (!currentOption && onChange) {
           onChange(null); // Clear selection if text doesn't match any option
        }
      }
      if (onBlur) onBlur(event);
    }, 150);
    setBlurTimeout(timeout);
  };

  const handleSuggestionClick = (suggestion) => {
    if (blurTimeout) clearTimeout(blurTimeout);

    setInputText(suggestion.label);
    setShowSuggestions(false);
    if (onChange) {
      // Pass a proper event object with name and value
      onChange(suggestion.value);
    }
    if (inputRef.current) inputRef.current.focus();
  };

  const renderDropdown = () => {
    if (!isBrowser || !showSuggestions || !filteredSuggestions?.length) return null;

    const dropdownContent = (
      <div
        className="fixed z-[9999] bg-white shadow-lg border border-gray-200 rounded-md overflow-hidden"
        style={{
          top: `${dropdownPosition.top}px`,
          left: `${dropdownPosition.left}px`,
          width: `${dropdownPosition.width}px`,
          maxHeight: '200px',
          overflowY: 'auto'
        }}
      >
        {filteredSuggestions.map((suggestion, index) => (
          <div
            key={index}
            className="py-2 px-3 text-sm cursor-pointer hover:bg-gray-100"
            onMouseDown={(e) => {
              e.preventDefault();
              handleSuggestionClick(suggestion);
            }}
          >
            {suggestion.label}
          </div>
        ))}
      </div>
    );
    return ReactDOM.createPortal(dropdownContent, document.body);
  };

  const hasValue = inputText && inputText.length > 0;

  return (
    <div className={twMerge("relative h-8 w-full min-w-[150px] rounded-md bg-white text-black", className)}>
      {!disabled && (
        <input
          ref={inputRef}
          type="text"
          className={twMerge(
            customClass,
            `${inputText && inputText.length > 0 && !hideExternalLabel ? 'border-t-transparent' : ''}`,
            `${placeholder !== '' && !hideExternalLabel ? 'focus:border-t-transparent' : ''}`
          )}
          name={name}
          value={inputText}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleBlurEvent}
          placeholder=" "
          autoComplete="off"
        />
      )}
      {disabled && (
        <p className={twMerge(
          customClass,
          `${inputText && inputText.length > 0 && !hideExternalLabel ? 'border-t-transparent' : ''}`
        )}>
          {options.find(opt => opt.value === value)?.label || value || ''}
        </p>
      )}

      {/* Placeholder label - similar to other input components */}
      {!hideExternalLabel && placeholder !== '' && (
        <p
          className={`before:content[' '] after:content[' '] peer-focus:before:border-t-1 peer-focus:after:border-t-1 peer-focus:after:border-r-1 peer-focus:before:border-l-1 pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none overflow-hidden text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-xs peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-[#ff7b7b] peer-focus:before:border-[#ff7b7b] peer-focus:after:border-[#ff7b7b] ${
            inputText && inputText.length > 0 ? 'text-[11px] leading-tight text-[#ff7b7b] before:border-[#ff7b7b] after:border-[#ff7b7b]' : ''
          }`}
        >
          {placeholder}
          {showRedAsteric && <span className="font-semibold text-red-500">&nbsp;*</span>}
        </p>
      )}

      {renderDropdown()}
    </div>
  );
};

AutoCompleteTextInput.propTypes = {
  name: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  placeholder: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    })
  ])).isRequired,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  optionMustBeSelected: PropTypes.bool,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
  className: PropTypes.string,
  hideExternalLabel: PropTypes.bool
};

// ----------------------------------------------------------------------

export const TextAreaInput = ({
  rows = 5,
  name,
  value,
  placeholder = '',
  onChange,
  disabled = false,
  showRedAsteric = true,
}) => (
  <div className="relative w-full min-w-[150px] rounded-md bg-white text-black dark:bg-gray-800 dark:text-white">
    <textarea
      rows={rows}
      type="string"
      className={twMerge(
        customClass,
        `${![undefined, null, '']?.includes(value) ? 'border-t-transparent' : ''}`,
        `${placeholder !== '' ? 'focus:border-t-transparent' : ''}`
      )}
      name={name}
      value={value}
      onChange={onChange}
      placeholder=" "
      autoComplete="off"
      disabled={disabled}
    />
    <p
      className={`before:content[' '] after:content[' '] peer-focus:before:border-t-1 peer-focus:after:border-t-1 peer-focus:after:border-r-1 peer-focus:before:border-l-1 pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none overflow-hidden text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-xs peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-[#ff7b7b] peer-focus:before:border-[#ff7b7b] peer-focus:after:border-[#ff7b7b]`}
    >
      {placeholder}
      {showRedAsteric && placeholder !== '' && (
        <span className="font-semibold text-red-500">&nbsp;*</span>
      )}
    </p>
  </div>
);

TextAreaInput.propTypes = {
  rows: PropTypes.number,
  name: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
};

// ----------------------------------------------------------------------

export const SelectInput = ({
  type = 'string',
  name,
  value,
  placeholder = '',
  defaultLabel = '-',
  onChange,
  options,
  disabled = false,
  showRedAsteric = true,
  allowOtherValue = false,
}) => {
  // Standard and Vars

  // Others
  const convertedOptions = (() =>
    options?.map((o) => {
      if (Object.prototype.toString.call(o) === '[object Object]') return o;
      return { value: o, label: String(o)?.toUpperCase() };
    }))();

  return (
    <div className="relative flex w-full min-w-[150px] flex-col gap-1 rounded-md bg-white text-black dark:bg-gray-800 dark:text-white">
      {!disabled && (
        <select
          className={twMerge(
            customClass,
            'h-8 py-0',
            `${placeholder !== '' ? 'border-t-transparent' : ''}`,
            `${placeholder !== '' ? 'focus:border-t-transparent' : ''}`
          )}
          name={name}
          value={
            // eslint-disable-next-line no-nested-ternary
            [undefined, '']?.includes(value)
              ? ''
              : !convertedOptions?.map((o) => o?.value)?.includes(value)
                ? '__other__'
                : value
          }
          onChange={onChange}
          placeholder=" "
        >
          <option value="" disabled hidden>
            {defaultLabel}
          </option>
          {convertedOptions.map((o, i) => (
            <option key={i} value={o?.value} className="text-xs">
              {o?.label}
            </option>
          ))}
          {allowOtherValue && (
            <option value="__other__" className="text-xs">
              OTHER
            </option>
          )}
        </select>
      )}
      {allowOtherValue && value === '__other__' && (
          <input
            type={type}
            className={twMerge(
              `focus:border-1 disabled:border-1 h-8 w-full  rounded-md border bg-transparent px-3 pb-1 pt-2 text-xs outline outline-0 transition-all placeholder-shown:border focus:border-[#ff7b7b] focus:outline-0 disabled:border-t-transparent disabled:outline-0`
            )}
            name={name}
            onBlur={onChange}
            placeholder="Please specify other"
            autoComplete="off"
          />
        )}
      {disabled && (
        <p
          className={twMerge(
            customClass,
            `${![undefined, null, '']?.includes(value) ? 'border-t-transparent' : ''}`
          )}
        >
          {getValue(value)}
        </p>
      )}
      {placeholder !== '' && (
        <p
          className={`before:content[' '] after:content[' '] peer-focus:before:border-t-1 peer-focus:after:border-t-1 peer-focus:after:border-r-1 peer-focus:before:border-l-1 pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none overflow-hidden text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-xs peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-[#ff7b7b] peer-focus:before:border-[#ff7b7b] peer-focus:after:border-[#ff7b7b]`}
        >
          {placeholder}
          {showRedAsteric && <span className="font-semibold text-red-500">&nbsp;*</span>}
        </p>
      )}
    </div>
  );
};

SelectInput.propTypes = {
  type: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  defaultLabel: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  options: PropTypes.array,
  showRedAsteric: PropTypes.bool,
  allowOtherValue: PropTypes.bool,
};

// ----------------------------------------------------------------------

export const BinarySwitchInput = ({
  name,
  value,
  placeholder = '',
  onChange,
  disabled = false,
  showRedAsteric = true,
}) => {
  // Standard and Vars
  const { asPath } = useRouter();
  const module = asPath?.split('?')?.[0]?.split('/')[1];

  return (
    <div className="flex w-full items-center gap-3 pl-2">
      <Switch
        name={name}
        onChange={(event) => {
          if (disabled) return;
          onChange({ target: { name, value: event.target.checked } });
        }}
        checked={value}
        disabled={disabled}
        sx={{
          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
            backgroundColor: getColorCode(module),
          },
        }}
      />
      <p className="whitespace-nowrap text-xs">
        {placeholder}{' '}
        {showRedAsteric && <span className="font-semibold text-red-500">&nbsp;*</span>}
      </p>
    </div>
  );
};

BinarySwitchInput.propTypes = {
  name: PropTypes.string,
  value: PropTypes.bool, // Changed from string to bool to match component implementation
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
};

// ----------------------------------------------------------------------

export const BinaryRadioInput = ({
  name,
  value,
  placeholder = '',
  onChange,
  disabled = false,
  showRedAsteric = true,
}) => {
  // Standard and Vars
  const { asPath } = useRouter();
  const module = asPath?.split('?')?.[0]?.split('/')[1];

  return (
    <button type="button" className="flex w-full items-center pl-2">
      <Radio
        name={name}
        onChange={(event) => {
          if (disabled) return;
          onChange({ target: { name, value: event.target.checked } });
        }}
        checked={value}
        disabled={disabled}
        sx={{
          '&:hover': {
            color: getColorCode(module),
          },
          '&.Mui-checked': {
            color: getColorCode(module),
          },
        }}
        size="small"
      />
      <p className="text-sm">
        {placeholder}{' '}
        {showRedAsteric && <span className="font-semibold text-red-500">&nbsp;*</span>}
      </p>
    </button>
  );
};

BinaryRadioInput.propTypes = {
  name: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
};

// ----------------------------------------------------------------------

export const SelectMultipleInput = ({
  name,
  value,
  placeholder = '',
  onChange,
  options = [],
  disabled = false,
  showRedAsteric = true,
}) => {
  // Standard and Vars
  const [popOverPosition, setPopOverPosition] = useState(null);

  const convertedOptions = () => [
    { value: 'all', label: 'ALL' },
    ...options?.map((o) => {
      if (Object.prototype.toString.call(o) === '[object Object]') return o;
      return { value: o, label: String(o)?.toUpperCase() };
    }),
  ];

  const getValueArray = () => {
    if (value) return JSON?.parse(value);
    return [];
  };

  return (
    <>
      <div className="relative h-8 w-full min-w-[150px] rounded-md bg-white text-black dark:bg-gray-800 dark:text-white">
        <button
          type="button"
          className={twMerge(
            customClass,
            `${![undefined, null, '']?.includes(value) ? 'border-t-transparent' : ''}`,
            `${placeholder !== '' ? 'focus:border-t-transparent' : ''}`
          )}
          name={name}
          value={value}
          onClick={(event) =>
            setPopOverPosition(popOverPosition === null ? event.currentTarget : null)
          }
          placeholder=" "
          autoComplete="off"
        >
          Select
        </button>

        <p
          className={`before:content[' '] after:content[' '] peer-focus:before:border-t-1 peer-focus:after:border-t-1 peer-focus:after:border-r-1 peer-focus:before:border-l-1 pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none overflow-hidden text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-xs peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-[#ff7b7b] peer-focus:before:border-[#ff7b7b] peer-focus:after:border-[#ff7b7b]`}
        >
          {placeholder}
          {showRedAsteric && placeholder !== '' && (
            <span className="font-semibold text-red-500">&nbsp;*</span>
          )}
        </p>
      </div>
      <Popover
        open={popOverPosition !== null}
        anchorEl={popOverPosition}
        onClose={() => setPopOverPosition(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        transformOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <div className="relative flex flex-col gap-1 px-2 py-1">
          {convertedOptions().map((o, index) => (
            // eslint-disable-next-line
            <li
              key={index}
              className={twMerge(
                'flex cursor-pointer flex-col justify-center p-1 text-xs  hover:bg-indigo-200',
                getValueArray()?.includes(o?.value) && 'bg-indigo-200'
              )}
              onClick={() => {
                if (onChange)
                  onChange({
                    target: {
                      name,
                      value: JSON?.stringify(
                        (() => {
                          let temp = getValueArray();
                          if (o?.value === 'all') return temp?.includes(o?.value) ? [] : [o?.value];
                          temp = temp?.filter((v) => v !== 'all');
                          return temp?.includes(o?.value)
                            ? temp?.filter((v) => v !== o?.value)
                            : [...temp, o?.value];
                        })()
                      ),
                    },
                  });
              }}
            >
              {o?.label}
            </li>
          ))}
        </div>
      </Popover>
    </>
  );
};

SelectMultipleInput.propTypes = {
  name: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  options: PropTypes.array,
  disabled: PropTypes.bool,
  showRedAsteric: PropTypes.bool,
};
