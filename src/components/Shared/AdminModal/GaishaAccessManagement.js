// React
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Typo<PERSON>, Tabs, Tab, Divider } from '@mui/material';

// Packages
import * as R from 'ramda';

// Components
import ProductAccessManagementCell from '../../admin/ProductAccessManagementCell';
import {
  SelectInput,
  SearchInput,
  AutoCompleteTextInput,
} from '../CustomInput';

// Utils
import axios from '../../../utils/axios';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useSnackbar } from '../snackbar';
import { getColorCode } from '../../../utils/shared';

// ----------------------------------------------------------------------

export default function GaishaAccessManagement() {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { allStaffs } = useSelector((state) => state.aum);

  const [toBeManuallyAssignedStaffId, setToBeManuallyAssignedStaffId] = useState(null);
  const [division, setDivision] = useState('all');
  const [module, setModule] = useState('gaisha');
  const [searchQuery, setSearchQuery] = useState('');

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);
  
  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const getTableData = () => {
    let temp = tableData;
    if (division !== 'all') {
      temp = temp.filter((o) => o?.division === division);
    }
    if (!searchQuery) {
      return temp;
    }
    return temp.filter((o) => JSON.stringify(o).toLowerCase().includes(searchQuery.toLowerCase()));
  };

  const getHeadCellStyle = () =>
    'whitespace-nowrap bg-[#408ed0] px-4 text-center text-white text-sm font-semibold';

  // Form
  const handleManualAssign = async (event) => {
    event?.preventDefault();
    try {
      const response = await axios.post(
        `${AUM_ENDPOINT}/user/v1/module/${toBeManuallyAssignedStaffId}`,
        {
          module,
          role: 'user',
          tag: '',
        }
      );

      if (response.data.status === 'success') {
        enqueueSnackbar(`${response.data.data}`);
      }
    } catch (error) {
      enqueueSnackbar(error?.message);
    }
    fetchData();
    setToBeManuallyAssignedStaffId(null);
  };

  const getDivisionList = () => R.uniq(R.pluck('division', tableData));

  const fetchData = async () => {
    dispatch(setIsLoading(true));

    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/${module}/all`);
      let filtered_user = response.data.data;

      filtered_user = filtered_user.filter(
        (o) => o.modules?.find((m) => m.module === module)?.role !== 'admin'
      );

      filtered_user.reverse();
      setTableData(filtered_user);
    } catch (error) {
      enqueueSnackbar('Failed to fetch TANYA access data', { variant: 'error' });
    }

    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [module]);

  return (
    <div className="h-full overflow-auto">
      <Typography variant="h6" className="mb-4">TANYA Access Management</Typography>
      
      <div className="flex px-4 mb-4">
        <Tabs
          value={module}
          onChange={(_, newValue) => setModule(newValue)}
          centered
          sx={{
            '& .MuiTabs-indicator': {
              backgroundColor: '#408ed0',
            },
          }}
          className="text-black dark:text-white"
        >
          {['gaisha', 'gaisha-dev', 'gaisha-preprod']?.map((o, i) => (
            <Tab key={i} label={o?.toUpperCase()} value={o} />
          ))}
        </Tabs>
      </div>

      <div className="flex flex-col gap-8 p-4">
        <p className="w-full text-center text-3xl font-bold md:text-left">
          {module?.toUpperCase()} Access Management
        </p>

        <Divider />
        
        <form
          className="mx-auto flex w-1/2 items-center justify-center gap-4"
          onSubmit={handleManualAssign}
        >
          <div className="w-[200px]">
            <AutoCompleteTextInput
              value={toBeManuallyAssignedStaffId}
              placeholder="Find a Staff"
              options={allStaffs?.map((o) => ({
                label: o?.name,
                value: o?.staff_id,
              }))}
              onChange={(event) => setToBeManuallyAssignedStaffId(event?.target?.value)}
            />
          </div>
          <button
            type="submit"
            className="rounded-[4px] bg-[#408ed0] px-2 py-1 text-sm font-bold text-white"
          >
            Assign
          </button>
        </form>
        
        <Divider />

        <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
          <div className="flex flex-col items-center gap-2 md:flex-row">
            <div className="w-[200px]">
              <SelectInput
                value={division}
                placeholder="Filter by Division"
                options={['all', ...getDivisionList().filter((o, i) => o !== '')]}
                onChange={(event) => setDivision(event.target.value)}
              />
            </div>
          </div>
          <SearchInput 
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="my-4 overflow-x-auto scrollbar">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                <td rowSpan={2} className={getHeadCellStyle()}>
                  No.
                </td>
                <td rowSpan={2} className={getHeadCellStyle()}>
                  Name
                </td>
                <td rowSpan={2} className={getHeadCellStyle()}>
                  Staff ID
                </td>
                <td colSpan={3} className={getHeadCellStyle()}>
                  Products
                </td>
              </tr>
              <tr>
                <td className={getHeadCellStyle()}>Name</td>
                <td className={getHeadCellStyle()}>Status</td>
                <td className={getHeadCellStyle()}>Action</td>
              </tr>
            </thead>
            <tbody>
              {getTableData()
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, i) => (
                  <ProductAccessManagementCell
                    key={row?.user_id}
                    rowData={row}
                    i={i}
                    rowsPerPage={rowsPerPage}
                    page={page}
                  />
                ))}
            </tbody>
          </table>
        </div>
        
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="text-center text-sm text-gray-500">
            Showing {getTableData().length} total TANYA users
          </div>
        </div>
      </div>
    </div>
  );
}
