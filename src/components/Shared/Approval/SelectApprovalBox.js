// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// Components
import { AutoCompleteTextInput } from '../CustomInput';
import { useSnackbar } from '../snackbar';

// Others
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { toUpperCaseFirstLetter } from '../../../utils/shared';

const SelectApprovalBox = ({
  TITLE = 'Approval List',
  GET_ALL_APPROVALS_ENDPOINT,
  UPDATE_CERTAIN_APPROVAL_ENDPOINT,
  disabled = false,
  onSaveStateChange,
  hideInternalSaveButton = false,
}) => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  const { allStaffs } = useSelector((state) => state.aum);
  const [approvalsData, setApprovalsData] = useState([]);
  const [originalData, setOriginalData] = useState([]);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Function to capitalize all name parts
  const formatName = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ') || [];
    return nameParts.map(part => part.toUpperCase()).join(' ');
  };

  const handleApprovalDataChange = async (payload) => {
    try {
      setIsSaving(true);
      const response = await axios.put(
        `${UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${payload?.id}`,
        payload
      );
      let variant = 'error';
      if ([200, 201, 202, 203, 204, 205]?.includes(response?.status)) {
        variant = 'success';
        await fetchData(); // Refresh data after successful update
        setHasChanges(false);
        
        // Dispatch a custom event that ApprovalStepper can listen for
        const approvalChangedEvent = new CustomEvent('approvalUpdated', {
          detail: { endpoint: GET_ALL_APPROVALS_ENDPOINT }
        });
        window.dispatchEvent(approvalChangedEvent);
      }
      enqueueSnackbar(toUpperCaseFirstLetter(variant), {
        variant,
      });
    } catch (error) {
      enqueueSnackbar('Failed to update approval', {
        variant: 'error',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveAllChanges = async () => {
    if (!hasChanges) return;
    
    setIsSaving(true);
    dispatch(setIsLoading(true));
    
    try {
      // Find the approver in the updated data
      const approverData = approvalsData.find(
        approval => approval.type?.toLowerCase() === 'approver'
      );
      
      if (approverData && approverData.id) {
        await handleApprovalDataChange(approverData);
        enqueueSnackbar('Approval workflow updated successfully', {
          variant: 'success',
        });
      } else {
        enqueueSnackbar('No approver data found to update', {
          variant: 'warning',
        });
      }
      
      setHasChanges(false);
    } catch (error) {
      enqueueSnackbar('Failed to save changes', {
        variant: 'error',
      });
    } finally {
      setIsSaving(false);
      dispatch(setIsLoading(false));
    }
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${GET_ALL_APPROVALS_ENDPOINT}`);
      if (response?.data?.data) {
        const data = response.data.data;
        setApprovalsData(data);
        setOriginalData(JSON.parse(JSON.stringify(data))); // Make a deep copy for comparison
        setHasChanges(false);
      }
    } catch {
      setApprovalsData([]);
      setOriginalData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Check for unsaved changes by comparing current data with original data
  useEffect(() => {
    if (originalData.length === 0 || approvalsData.length === 0) return;
    
    const approverOriginal = originalData.find(a => a.type?.toLowerCase() === 'approver');
    const approverCurrent = approvalsData.find(a => a.type?.toLowerCase() === 'approver');
    
    if (approverOriginal && approverCurrent) {
      // Compare the approver's user_name_array
      const originalNames = JSON.stringify(approverOriginal.user_name_array);
      const currentNames = JSON.stringify(approverCurrent.user_name_array);
      
      setHasChanges(originalNames !== currentNames);
    }
  }, [approvalsData, originalData]);

  // Group approval data by role order
  const getOrderedApprovals = () => {
    // Define the preferred order of approval types
    const approvalOrder = ['requestor', 'preparer', 'approver', 'reviewer', 'endorser'];
    
    // Sort the approvals data by the preferred order
    const orderedData = [...approvalsData].sort((a, b) => {
      const aIndex = approvalOrder.indexOf(a.type?.toLowerCase());
      const bIndex = approvalOrder.indexOf(b.type?.toLowerCase());
      
      // If both types are found in the order array, sort by their position
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      
      // If only one type is found, prioritize it
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      
      // If neither is found in the preferred order, keep their original order
      return 0;
    });
    
    return orderedData;
  };

  // Check if input should be disabled based on approval type
  const isInputDisabled = (approvalType) => {
    const type = approvalType?.toLowerCase();
    return (
      disabled || 
      ['requestor', 'preparer', 'reviewer', 'endorser'].includes(type) ||
      approvalsData.find(a => a.type?.toLowerCase() === type)?.is_disabled
    );
  };

  // Notify parent component about save state changes
  useEffect(() => {
    if (onSaveStateChange) {
      onSaveStateChange({
        hasChanges,
        isSaving,
        disabled,
        onSave: handleSaveAllChanges
      });
    }
  }, [hasChanges, isSaving, disabled, onSaveStateChange]);

  return (
    <div className="flex w-full flex-col gap-4 rounded-lg border border-slate-300 bg-white p-6 text-black scrollbar-none">
      {/* Show internal save button only if not hidden */}
      {!hideInternalSaveButton && hasChanges && (
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium">{TITLE}</p>
          <button
            type="button"
            className="bg-blue-600 hover:bg-blue-700 whitespace-nowrap rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleSaveAllChanges}
            disabled={isSaving || !hasChanges || disabled}
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      )}

      {/* Display all approvals in a 2-column grid layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {getOrderedApprovals().map((approval, index) => (
          <div 
            key={`approval-${approval.id || index}`} 
            className={`flex flex-col gap-1 p-3 rounded-md ${approval.type?.toLowerCase() === 'approver' ? 'bg-blue-50/50 border border-blue-100' : ''}`}
          >
            <p className="text-xs font-medium">{approval.type?.toUpperCase()}</p>
            {approval?.user_name_array?.map((userName, j) => (
              <AutoCompleteTextInput
                key={`approval-${approval.id}-field-${j}`}
                value={userName || ''}
                options={allStaffs?.map((q) => ({
                  label: formatName(q?.name),
                  value: q?.name
                }))}
                onChange={(valueOrEvent) => {
                  // Handle both direct value (from autocomplete selection) and event object (from typing)
                  const selectedValue = typeof valueOrEvent === 'string' 
                    ? valueOrEvent 
                    : valueOrEvent?.target?.value;
                  
                  setApprovalsData((prev) => {
                    const temp = [...prev];
                    const originalIndex = prev.findIndex(item => item.id === approval.id);
                    if (originalIndex !== -1) {
                      temp[originalIndex].user_name_array[j] = selectedValue || '';
                    }
                    return temp;
                  });
                }}
                disabled={isInputDisabled(approval.type)}
                className={approval.type?.toLowerCase() === 'approver' ? 'border-blue-200 focus:ring-blue-500' : ''}
                hideExternalLabel={true}
                placeholder={
                  approval.type?.toLowerCase() === 'approver' && !userName
                    ? 'Type to search and select an approver...'
                    : undefined
                }
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

SelectApprovalBox.propTypes = {
  TITLE: PropTypes.string,
  GET_ALL_APPROVALS_ENDPOINT: PropTypes.string,
  UPDATE_CERTAIN_APPROVAL_ENDPOINT: PropTypes.string,
  disabled: PropTypes.bool,
  onSaveStateChange: PropTypes.func,
  hideInternalSaveButton: PropTypes.bool,
};

export default SelectApprovalBox;
