// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// MUI
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Components
import { useSnackbar } from '../Shared/snackbar';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSimiContext } from '../../utils/simi';

/**
 * A styled version of FaApproveRejectButtons component that directly matches
 * the Export Summary button styling in the project page.
 */
const FaApproveRejectButtons = (props) => {
  const { moduleColorCode } = useSimiContext();
  const { user } = useAuthContext();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { asPath } = useRouter();
  const { handleSendEmail, getCertainStaffInfoFromStaffId } = useSimiContext();
  
  const [approvalsData, setApprovalsData] = useState([]);
  const [type, setType] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const currentStageIndex = approvalsData?.findIndex((o) => o?.status?.toLowerCase() === 'pending');
  const currentStageData = approvalsData?.[currentStageIndex];
  
  const handleRevert = async () => {
    try {
      // Update Approvals
      for (let i = 0; i < approvalsData?.length; i += 1) {
        await axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${approvalsData[i]?.id}`, {
          ...approvalsData[i],
          status: 'pending',
        });
      }

      // Send E-Mail
      const recipientsList = approvalsData?.[0]?.user_name_array;
      const ccsList = [];

      await handleSendEmail(
        recipientsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        ccsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        `${props.NAME} - ${type === 'revert' ? 'REVERTED' : 'ESCALATED'}`,
        `
          <p>${props.NAME} has been ${type === 'revert' ? 'reverted' : 'escalated'} to you.</p>\n
          <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
        `
      );

      // Callback
      if (props.REVERT_CALLBACK) props.REVERT_CALLBACK();

      // Success
      enqueueSnackbar('Success!', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Something went wrong!', {
        variant: 'error',
      });
    }
    fetchData();
  };

  const handleSubmit = async () => {
    try {
      // Update Approval
      await axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${currentStageData?.id}`, {
        ...currentStageData,
        status: 'done',
      });

      // Send E-Mail
      const recipientsList = approvalsData?.[currentStageIndex + 1]?.user_name_array;
      const ccsList = (() => {
        const temp = [];
        for (let i = 0; i < currentStageIndex + 1; i += 1) {
          temp.push(...approvalsData?.[i]?.user_name_array);
        }
        return temp;
      })();

      await handleSendEmail(
        recipientsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        ccsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        `${props.NAME} - ${type === 'revert' ? 'REVERTED' : 'ESCALATED'}`,
        `
          <p>${props.NAME} has been ${type === 'revert' ? 'reverted' : 'escalated'} to you.</p>\n
          <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
        `
      );

      // Callback
      if (props.SUBMIT_CALLBACK)
        props.SUBMIT_CALLBACK(`pending ${approvalsData?.[currentStageIndex + 1]?.type}`);

      enqueueSnackbar('Success!', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Something went wrong!', {
        variant: 'error',
      });
    }
    fetchData();
  };

  const handleApprove = async () => {
    try {
      // Update Approval
      await axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${currentStageData?.id}`, {
        ...currentStageData,
        status: 'done',
      });

      // Send E-Mail
      const getRecipientList = () => approvalsData?.[currentStageIndex + 1]?.user_name_array;
      const getCCList = () => {
        const temp = [];
        for (let i = 0; i < currentStageIndex + 1; i += 1) {
          temp.push(...approvalsData?.[i]?.user_name_array);
        }
        return temp;
      };

      await handleSendEmail(getRecipientList(), getCCList());

      // Callback
      if (props.APPROVE_CALLBACK)
        props.APPROVE_CALLBACK(
          approvalsData?.[currentStageIndex + 1]?.type
            ? `pending ${approvalsData?.[currentStageIndex + 1]?.type}`
            : 'completed'
        );

      enqueueSnackbar('Success!', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Something went wrong!', {
        variant: 'error',
      });
    }
    fetchData();
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${props.GET_ALL_APPROVALS_ENDPOINT}`);
      if (response.data.data) {
        setApprovalsData(response.data.data);
      }
    } catch {
      setApprovalsData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  if (props.disabled) return null;
  
  return (
    <>
      {currentStageData?.user_name_array?.includes(user?.name) && (
        <div className="flex items-center gap-3">
          {currentStageIndex === 0 && (
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              onClick={() => {
                setType('submit');
                setDialogOpen(true);
              }}
            >
              Submit
            </button>
          )}
          {currentStageIndex !== 0 && (
            <>
              <button
                type="button"
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500/50"
                onClick={() => {
                  setType('revert');
                  setDialogOpen(true);
                }}
              >
                Revert
              </button>
              <button
                type="button"
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500/50"
                onClick={() => {
                  setType('approve');
                  setDialogOpen(true);
                }}
              >
                Approve
              </button>
            </>
          )}
        </div>
      )}

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle 
          className="bg-fa text-center text-white"
          style={{ backgroundColor: moduleColorCode }}
        >
          {toUpperCaseFirstLetter(type)} {props.documentName || 'Project'}
        </DialogTitle>
        <DialogContent>
          <div className="mt-8 flex w-full flex-col items-center gap-4 p-2 md:w-[400px]">
            Proceed?
          </div>
        </DialogContent>
        <DialogActions className="border-t border-slate-200 bg-slate-50 px-6 py-4">
          <div className="flex w-full justify-between">
            <button 
              type="button" 
              onClick={() => setDialogOpen(false)} 
              className="px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={async () => {
                if (type === 'revert') handleRevert();
                else if (type === 'submit') handleSubmit();
                else if (type === 'approve') handleApprove();
                setDialogOpen(false);
              }}
              className={`px-4 py-2 text-sm font-medium text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 ${
                type === 'revert' 
                  ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500/50' 
                  : type === 'approve'
                  ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500/50'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500/50'
              }`}
            >
              Proceed
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FaApproveRejectButtons;
