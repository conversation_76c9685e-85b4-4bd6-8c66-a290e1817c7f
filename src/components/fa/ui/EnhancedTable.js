import React from 'react';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';

/**
 * Enhanced Table Component for FA Module
 * Provides professional table styling with better readability and user experience
 */
export const EnhancedTable = ({ 
  children, 
  className,
  variant = 'default',
  size = 'normal',
  ...props 
}) => {
  const baseClasses = 'min-w-full bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200';
  
  const variants = {
    default: '',
    striped: '[&_tbody_tr:nth-child(even)]:bg-gray-50',
    bordered: 'border-2 border-gray-300',
    compact: '[&_td]:py-2 [&_th]:py-2'
  };

  const sizes = {
    small: 'text-xs',
    normal: 'text-sm',
    large: 'text-base'
  };

  return (
    <div className="overflow-x-auto">
      <table
        className={twMerge(baseClasses, variants[variant], sizes[size], className)}
        {...props}
      >
        {children}
      </table>
    </div>
  );
};

/**
 * Enhanced Table Header with gradient background
 */
export const EnhancedTableHeader = ({ 
  children, 
  className,
  variant = 'primary',
  ...props 
}) => {
  const variants = {
    primary: 'bg-gradient-to-r from-blue-600 to-blue-700',
    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700',
    success: 'bg-gradient-to-r from-green-600 to-green-700',
    warning: 'bg-gradient-to-r from-yellow-600 to-yellow-700',
    danger: 'bg-gradient-to-r from-red-600 to-red-700'
  };

  return (
    <thead
      className={twMerge(variants[variant], 'text-white text-sm font-medium', className)}
      {...props}
    >
      {children}
    </thead>
  );
};

/**
 * Enhanced Table Header Cell
 */
export const EnhancedTableHeaderCell = ({ 
  children, 
  className,
  sortable = false,
  sortDirection,
  onSort,
  align = 'left',
  ...props 
}) => {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  const handleSort = () => {
    if (sortable && onSort) {
      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      onSort(newDirection);
    }
  };

  return (
    <th
      className={twMerge(
        'px-4 py-3 font-medium text-white border-r border-blue-500 last:border-r-0',
        alignClasses[align],
        sortable ? 'cursor-pointer hover:bg-blue-800 transition-colors' : '',
        className
      )}
      onClick={handleSort}
      {...props}
    >
      <div className="flex items-center gap-2">
        <span>{children}</span>
        {sortable && (
          <div className="flex flex-col">
            <svg
              className={twMerge(
                'w-3 h-3',
                sortDirection === 'asc' ? 'text-white' : 'text-blue-300'
              )}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
            </svg>
          </div>
        )}
      </div>
    </th>
  );
};

/**
 * Enhanced Table Body
 */
export const EnhancedTableBody = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <tbody
      className={twMerge('divide-y divide-gray-200', className)}
      {...props}
    >
      {children}
    </tbody>
  );
};

/**
 * Enhanced Table Row
 */
export const EnhancedTableRow = ({ 
  children, 
  className,
  clickable = false,
  selected = false,
  onClick,
  ...props 
}) => {
  return (
    <tr
      className={twMerge(
        'transition-colors duration-150',
        clickable ? 'hover:bg-blue-50 cursor-pointer' : '',
        selected ? 'bg-blue-100' : '',
        className
      )}
      onClick={onClick}
      {...props}
    >
      {children}
    </tr>
  );
};

/**
 * Enhanced Table Cell
 */
export const EnhancedTableCell = ({ 
  children, 
  className,
  align = 'left',
  variant = 'default',
  ...props 
}) => {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  const variants = {
    default: 'text-gray-900',
    muted: 'text-gray-600',
    success: 'text-green-700',
    warning: 'text-yellow-700',
    danger: 'text-red-700',
    primary: 'text-blue-700'
  };

  return (
    <td
      className={twMerge(
        'px-4 py-3 border-r border-gray-200 last:border-r-0',
        alignClasses[align],
        variants[variant],
        className
      )}
      {...props}
    >
      {children}
    </td>
  );
};

/**
 * Table Actions Component for action buttons in table rows
 */
export const TableActions = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div
      className={twMerge('flex items-center gap-2', className)}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Table Action Button
 */
export const TableActionButton = ({ 
  children, 
  className,
  variant = 'default',
  size = 'small',
  ...props 
}) => {
  const variants = {
    default: 'text-gray-600 hover:text-gray-900 hover:bg-gray-100',
    primary: 'text-blue-600 hover:text-blue-900 hover:bg-blue-100',
    success: 'text-green-600 hover:text-green-900 hover:bg-green-100',
    warning: 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-100',
    danger: 'text-red-600 hover:text-red-900 hover:bg-red-100'
  };

  const sizes = {
    small: 'p-1',
    normal: 'p-2',
    large: 'p-3'
  };

  return (
    <button
      className={twMerge(
        'rounded-md transition-colors duration-150',
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

/**
 * Empty State Component for tables with no data
 */
export const TableEmptyState = ({ 
  message = 'No data available',
  icon,
  action,
  className,
  ...props 
}) => {
  return (
    <tr>
      <td colSpan="100%" className={twMerge('px-6 py-12 text-center', className)} {...props}>
        <div className="flex flex-col items-center gap-4">
          {icon && (
            <div className="w-12 h-12 text-gray-400">
              {icon}
            </div>
          )}
          <div className="text-gray-500 text-sm">{message}</div>
          {action && (
            <div>
              {action}
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

// PropTypes
EnhancedTable.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'striped', 'bordered', 'compact']),
  size: PropTypes.oneOf(['small', 'normal', 'large'])
};

EnhancedTableHeader.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'warning', 'danger'])
};

EnhancedTableHeaderCell.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  sortable: PropTypes.bool,
  sortDirection: PropTypes.oneOf(['asc', 'desc']),
  onSort: PropTypes.func,
  align: PropTypes.oneOf(['left', 'center', 'right'])
};

EnhancedTableBody.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string
};

EnhancedTableRow.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  clickable: PropTypes.bool,
  selected: PropTypes.bool,
  onClick: PropTypes.func
};

EnhancedTableCell.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  align: PropTypes.oneOf(['left', 'center', 'right']),
  variant: PropTypes.oneOf(['default', 'muted', 'success', 'warning', 'danger', 'primary'])
};

export default EnhancedTable;
