// Next, React, Tw
import { useState, useEffect, Fragment } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import * as yup from 'yup';
import moment from 'moment';
import PropTypes from 'prop-types';
import DocViewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';

// Display Utils
import { getDisplayMode, generatePeriodLabels, convertYearlyToMonthly } from '../../utils/faDisplayUtils';

// Components
import { TextInput, SelectInput, DateInput, BinarySwitchInput, BinaryRadioInput } from '../Shared/CustomInput';
import UploadCsvButton from '../Shared/UploadCsv';
import ScreenshotDialog from '../Shared/ScreenshotDialog';
import ExportExcelButton from '../Shared/ExportExcelButton';
import { EnhancedCard, EnhancedCardHeader, EnhancedCardContent } from './ui/EnhancedCard';
import { EnhancedTable, EnhancedTableHeader, EnhancedTableHeaderCell, EnhancedTableBody, EnhancedTableRow, EnhancedTableCell } from './ui/EnhancedTable';
import { EnhancedButton, IconButton } from './ui/EnhancedButton';

// Others
import { useSnackbar } from '../Shared/snackbar';
import axios from '../../utils/axios';
import { FA_ENDPOINT } from '../../utils/fa';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { checkAndReplaceNumberWithZero } from '../../utils/shared';
import { setBreadCrumbsList } from '../../utils/store/simiReducer';

// Following OPEX's simple approach

const AllCapex = ({ scenarioId: propScenarioId, showExportButton = true, onDataLoaded, exportMode = false }) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { projectId, scenarioId } = query;
  const dispatch = useDispatch();
  const { isAdmin } = useModuleRoleContext();
  const { projectData, scenarioData } = useSelector((state) => state.fa);

  const [capexData, setCapexData] = useState([]);

  const IS_DISABLED = !isAdmin || projectData?.IS_DISABLED;

  // Clean, simple styling (following OPEX pattern)
  const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200 text-center';
  const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';
  const numberCellStyle = 'text-right py-3 text-sm border-b border-gray-100 px-4 font-mono text-gray-700';
  const subtotalStyle = 'bg-gray-50 font-semibold text-center py-3 text-sm border-b border-gray-200 px-4 text-gray-800';
  const grandTotalStyle = 'bg-blue-50 font-bold text-center py-3 text-sm border-b border-blue-200 px-4 text-blue-900';

  // Determine display mode based on contract period
  const { isMonthlyDisplay, totalPeriods, contractMonths, startGapMonths } = getDisplayMode(scenarioData);

  // Generate period labels (M0, M1, etc. or Y0, Y1, etc.)
  const periodLabels = generatePeriodLabels(isMonthlyDisplay, totalPeriods);

  // Helper function to convert yearly data to monthly data for display
  const convertToDisplayFormat = (item, yearlyArrayKey) => {
    if (!item || !item[yearlyArrayKey] || !item[yearlyArrayKey].length) {
      // Return array of zeros with correct length to match periodLabels
      return Array(totalPeriods).fill(0);
    }

    if (isMonthlyDisplay) {
      // Convert yearly data to monthly data, accounting for the gap between project start and RFS date
      return convertYearlyToMonthly(item[yearlyArrayKey], contractMonths, startGapMonths);
    }

    // Return the original yearly data
    return item[yearlyArrayKey];
  };

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const [omType, setOmType] = useState('standard'); // 'standard' or 'custom'
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }

    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const schema = yup.object({
    amount: yup.number().required('Please provide amount.'),
    currency: yup.string().required('Please provide currency.'),
    custom_rate: yup.number(),
    depreciation_summary_by_year_array: yup.array().of(yup.number()).default([]),
    details: yup.string().required('Please provide details.'),
    no_of_site: yup.number().required('Please provide no. of site.'),
    om_needed: yup.boolean().required('Please provide O&M needed.')?.default(false),
    om_summary_by_year_array: yup.array().of(yup.number()).default([]),
    payment_term_in_days: yup.number().required('Please provide payment term.'),
    project_id: yup.string().required('Please provide project ID.')?.default(projectId),
    provider: yup.string().required('Please provide provider.'),
    scenario_id: yup.string().required('Please provide scenario ID.')?.default(scenarioId),
    leg_a: yup.string().required('Please provide leg A.'),
    leg_b: yup.string().required('Please provide leg B.'),
  });

  const getContractPeriodInYearsArray = () => {
    const temp = scenarioData.contract_period_in_month % 12;
    const momentDate = moment(scenarioData.expected_rfs_date);
    const startYear = moment(scenarioData.project_start_date).year();
    const rfsYear = moment(scenarioData.expected_rfs_date).year();
    const yearDifference = Math.max(0, rfsYear - startYear);

    let contractYears;
    if (momentDate.date() === 1 && momentDate.month() === 0) {
      contractYears = (scenarioData.contract_period_in_month - temp) / 12;
    } else {
      contractYears = (scenarioData.contract_period_in_month - temp) / 12 + 1;
    }

    // Total years = gap years + contract years
    const totalYears = yearDifference + contractYears;

    return Array.from(
      { length: totalYears },
      (_, index) => index
    );
  };

  const getNetOmArray = (data) => {
    const startYear = moment(scenarioData.project_start_date).year();
    const rfsYear = moment(scenarioData.expected_rfs_date).year();
    const yearDifference = Math.max(0, rfsYear - startYear);

    const contractYears = getContractPeriodInYearsArray()?.length || 0;
    // Total years needed = gap years + contract years
    const totalYears = yearDifference + contractYears;
    const temp = Array(totalYears).fill(0);

    // Apply O&M rates after the year difference
    for (let i = yearDifference; i < totalYears; i += 1) {
      if (data?.om_type === 'custom' && data?.custom_year1_rate) {
        // Custom O&M: use flat rate for all applicable years
        temp[i] = data.amount * (parseFloat(data.custom_year1_rate) / 100);
      } else {
        // Standard O&M
        if (i === yearDifference) {
          // First applicable year: Apply first_year_om percentage
          temp[i] = data.amount * (projectData?.rates_locked_during_creation?.first_year_om / 100);
        } else {
          // Subsequent years: Increase previous year's amount by increment
          const prevYearAmount = temp[i - 1];
          const incrementRate = projectData?.rates_locked_during_creation?.om_annual_increment / 100;
          temp[i] = prevYearAmount * (1 + incrementRate);
        }
      }
    }
    return temp;
  };

  const getMetrix = () => {
    const getAllMonthsArray = () => Array.from({ length: 12 }, (_, index) => index);

    const getFirstMonthNoOfDays = () => {
      const givenDate = moment(scenarioData?.expected_rfs_date);
      const totalDaysInMonthOfTheGivenDate = givenDate.daysInMonth();
      const dayOfMonth = givenDate.date();
      const remainingDays = totalDaysInMonthOfTheGivenDate - dayOfMonth + 1;
      return remainingDays;
    };

    const getFirstMonthNo = () => {
      const givenDate = moment(scenarioData?.expected_rfs_date);
      const monthNo = givenDate.month();
      return monthNo;
    };

    const getLastMonthNoOfDays = () => {
      const givenDate = moment(scenarioData?.expected_rfs_date)
        ?.clone()
        .add(scenarioData?.contract_period_in_month, 'months')
        ?.subtract(1, 'days');
      const dayOfMonth = givenDate.date();
      return dayOfMonth;
    };
    const getLastMonthNo = () => {
      const givenDate = moment(scenarioData?.expected_rfs_date)
        ?.clone()
        .add(scenarioData?.contract_period_in_month, 'months')
        ?.subtract(1, 'days');

      const monthNo = givenDate.month();

      return monthNo;
    };
    const temp = [];

    // Note : The metrix is 2D, 1st array is for years, 2nd array is for months and they are zero indexed, eg: temp[1][2] means 2nd year, 3rd month

    const startYear = moment(scenarioData.project_start_date).year();
    const rfsYear = moment(scenarioData.expected_rfs_date).year();
    const yearDifference = Math.max(0, rfsYear - startYear);

    for (let i = 0; i < getContractPeriodInYearsArray().length; i += 1) {
      const temp2 = [];

      // If this is a gap year (before RFS date), fill with zeros
      if (i < yearDifference) {
        temp2.push(...Array(12).fill(0));
      } else {
        // Adjust index for active contract years
        const activeYear = i - yearDifference;

        for (let j = 0; j < getAllMonthsArray().length; j += 1) {
          if (activeYear === 0 && j < getFirstMonthNo()) {
            temp2?.push(0);
          } else if (activeYear === 0 && j === getFirstMonthNo()) {
            temp2?.push(getFirstMonthNoOfDays());
          } else if (
            activeYear === getContractPeriodInYearsArray().length - yearDifference - 1 &&
            j > getLastMonthNo()
          ) {
            temp2?.push(0);
          } else if (
            activeYear === getContractPeriodInYearsArray().length - yearDifference - 1 &&
            j === getLastMonthNo()
          ) {
            temp2?.push(getLastMonthNoOfDays());
          } else {
            temp2?.push(
              moment(scenarioData?.expected_rfs_date)
                ?.clone()
                .startOf('year')
                ?.add(activeYear, 'years')
                ?.add(j, 'months')
                .daysInMonth()
            );
          }
        }
      }
      temp?.push(temp2);
    }
    return temp;
  };

  const getTotalNoOfDaysEntireContract = () => {
    let temp = 0;
    for (let i = 0; i < getMetrix().length; i += 1) {
      for (let j = 0; j < getMetrix()[i].length; j += 1) {
        temp += getMetrix()[i][j];
      }
    }
    return temp;
  };

  const handleDialogClose = async (action, payload) => {
    if (action) {
      try {
        payload = await schema.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      const temporaryHolder = { amount: payload.amount };

      // Add scenario dates to payload
      payload.expected_rfs_or_handover_date = scenarioData.expected_rfs_date;
      payload.project_start_or_expected_gr_or_capex_released_date = scenarioData.project_start_date;

      // Standardize with project currency
      if (projectData?.currency !== payload?.currency) {
        if (projectData?.exchange_rate === 0) {
          enqueueSnackbar("Exchange rate can't be 0", {
            variant: 'error',
          });
          return;
        }
        if (projectData?.currency === 'myr') {
          payload.amount *= projectData?.exchange_rate;
        } else {
          payload.amount /= projectData?.exchange_rate;
        }
      }

      // Calculate O&M values with day-based proration
      if (payload.om_needed) {
        // Use getNetOmArray to get the correct annual O&M for each year (with increment)
        const netOmArray = getNetOmArray(payload);
        const startYear = moment(scenarioData.project_start_date).year();
        const rfsYear = moment(scenarioData.expected_rfs_date).year();
        const yearDifference = Math.max(0, rfsYear - startYear);

        payload.om_summary_by_year_array = getMetrix()?.map((o, i) => {
          // For gap years, use project start date as base
          const baseDate = i < yearDifference
            ? moment(scenarioData.project_start_date).clone().add(i, 'years')
            : moment(scenarioData.expected_rfs_date).clone().add(i - yearDifference, 'years');

          return (netOmArray[i] / (baseDate.isLeapYear() ? 366 : 365)) *
                 o.reduce((prev, curr) => prev + curr, 0);
        });
      } else {
        payload.om_summary_by_year_array = [];
      }

      // Calculate depreciation based on year difference between start date and RFS date
      const startYear = moment(scenarioData.project_start_date).year();
      const rfsYear = moment(scenarioData.expected_rfs_date).year();
      const yearDifference = Math.max(0, rfsYear - startYear);

      const contractYears = getContractPeriodInYearsArray(
        scenarioData.expected_rfs_date
      ).length;

      // Total years needed = gap years + contract years
      const totalYears = yearDifference + contractYears;

      // Calculate base annual depreciation
      const annualDepreciation = payload.amount / contractYears;
      const metrix = getMetrix();

      // Calculate total days in contract period for normalization
      const totalContractDays = metrix
        .slice(yearDifference, yearDifference + contractYears)
        .reduce((sum, yearDays) => sum + yearDays.reduce((a, b) => a + b, 0), 0);

      payload.depreciation_summary_by_year_array = metrix.map((daysArray, i) => {
        if (i < yearDifference) {
          // Fill gap years with 0
          return 0;
        } else if (i < yearDifference + contractYears) {
          // For active years, calculate depreciation proportional to days
          const daysInYear = daysArray.reduce((prev, curr) => prev + curr, 0);
          // Scale by total contract days to ensure full amount is depreciated
          return annualDepreciation * (daysInYear / (totalContractDays / contractYears));
        } else {
          // Any remaining years (shouldn't occur) get 0
          return 0;
        }
      });

      payload = { ...payload, ...temporaryHolder };

      dispatch(setIsLoading(true));
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${FA_ENDPOINT}/capex`, payload);
            break;
          case 'put':
            response = await axios.put(`${FA_ENDPOINT}/capex/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${FA_ENDPOINT}/capex/${payload?.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
      dispatch(setIsLoading(false));
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/capex/scenario_id/${scenarioId}`);

      if (response?.data?.data) {
        setCapexData(
          response?.data?.data?.map((o) => ({
            ...o,
            om_summary_total: o?.om_summary_by_year_array?.reduce((prev, curr) => prev + curr, 0),
            depreciation_summary_total: o?.depreciation_summary_by_year_array?.reduce(
              (prev, curr) => prev + curr,
              0
            ),
          }))
        );
      }
    } catch {
      setCapexData([]);
    }
    dispatch(setIsLoading(false));
  };

  // State for file handling
  const [fileList, setFileList] = useState([]);
  const [focusedFile, setFocusedFile] = useState({});
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pdfUrl, setPdfUrl] = useState(null);

  // File functions
  const fetchFileList = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/file/capex/${scenarioId}`);
      console.log('Full file list response:', response.data);

      // Check what properties are available in the file objects
      if (response?.data?.data?.length > 0) {
        console.log('First file object:', JSON.stringify(response.data.data[0], null, 2));
        console.log('All keys in first file object:', Object.keys(response.data.data[0]));

        // Print all files for debugging
        response.data.data.forEach((file, index) => {
          console.log(`File ${index + 1}:`, JSON.stringify(file, null, 2));
        });
      } else {
        console.log('No files returned from API');
      }

      setFileList(response?.data?.data || []);
    } catch (error) {
      console.error('Error fetching file list:', error);
      setFileList([]);
    }
    dispatch(setIsLoading(false));
  };

  const downloadFile = async (file) => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/file/download/${file?.id}`, {
        responseType: 'blob',
      });

      const type = file?.file_name.split('.').pop();

      const typeMimeDictionary = {
        pdf: 'application/pdf',
      };

      if (Object?.keys(typeMimeDictionary)?.includes(type)) {
        const blobUrl = URL.createObjectURL(
          new Blob([response?.data], { type: typeMimeDictionary[type] })
        );
        setPdfUrl(blobUrl);
        setParam({ viewAttachmentDialogOpen: 'true' });
      } else {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(response?.data);
        link.download = file?.file_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (scenarioId) {
      fetchData();
      fetchFileList(); // Add this to fetch file list when component mounts
    }
  }, []);

  // Add effect to call onDataLoaded when capexData changes
  useEffect(() => {
    if (onDataLoaded && capexData) {
      onDataLoaded(capexData);
    }
  }, [capexData, onDataLoaded]);

  useEffect(() => {
    dispatch(
      setBreadCrumbsList([
        {
          linkTo: '/fa',
          label: 'FA List',
        },
        {
          linkTo: `/fa/project/${projectId}`,
          label: projectData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=capex`,
          label: scenarioData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=capex`,
          label: 'CAPEX',
        },
      ])
    );
  }, [projectId, projectData?.name, scenarioData?.name, scenarioId]);

  // ParamContext for export button
  const { setParam } = useParamContext();

  return (
    <>
      {showExportButton && !exportMode && (
        <div className="mb-4 flex w-full justify-end gap-4">
          <button
            type="button"
            className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
            onClick={() => {
              setParam({ screenshotDialogOpen: true });
            }}
          >
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Export Details
          </button>
        </div>
      )}
      <div className="flex items-center justify-between mb-6">
        <p className="text-lg font-bold">CAPEX INVESTMENT</p>
        {!IS_DISABLED && (
          <div className="flex items-center gap-3">
            <ExportExcelButton
              data={[
                {
                  amount: 0,
                  currency: 'myr',
                  custom_rate: 0,
                  details: 'string',
                  expected_rfs_or_handover_date: '2012-12-21',
                  no_of_site: 0,
                  payment_term_in_days: 0,
                  project_start_or_expected_gr_or_capex_released_date: '2012-12-21',
                  provider: 'string',
                },
              ]}
              filename="capex_template.csv"
            >
              <button
                type="button"
                className="flex items-center gap-1.5 rounded-lg border border-gray-300 bg-white/90 px-3 py-1.5 text-xs font-medium text-gray-600 shadow-sm backdrop-blur-sm transition-all duration-200 hover:bg-white"
              >
                <svg
                  className="h-3.5 w-3.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Template
              </button>
            </ExportExcelButton>

            <UploadCsvButton
              onUpload={async (data) => {
                for (let i = 0; i < data?.length; i += 1) {
                  await handleDialogClose('post', data?.[i]);
                }
              }}
            >
              <button
                type="button"
                className="flex items-center gap-1.5 whitespace-nowrap rounded-lg bg-gray-200 px-3 py-1.5 text-xs font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-300"
              >
                <svg
                  className="h-3.5 w-3.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                  />
                </svg>
                Upload CSV
              </button>
            </UploadCsvButton>
            <button
              type="button"
              className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-all duration-200"
              onClick={() => handleClickOpenDialog(false)}
            >
              <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add
            </button>
          </div>
        )}
      </div>
            <div className="overflow-x-auto mb-10">
        <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
          <thead>
            <tr>
              <td className={twMerge(headerCellStyle, 'w-[30px]')}>
                No.
              </td>
                {[
                  { label: 'Provider', align: 'center' },
                  { label: 'Details', align: 'center' },
                  { label: 'No. of Site', align: 'right' },
                  { label: 'Payment Term (Days)', align: 'right' },
                  { label: `Amount (${projectData?.currency?.toUpperCase()})`, align: 'right' },
                  { label: 'Start Date', align: 'center' },
                  { label: 'RFS Date', align: 'center' },
                ].map((item, i) => (
                  <td
                    key={i}
                    className={twMerge(
                      headerCellStyle, 
                      'w-[150px]',
                      item.align === 'right' ? 'text-right' : ''
                    )}
                  >
                    {item.label}
                  </td>
                ))}
              </tr>
            </thead>
          <tbody>
            {capexData.map((row, i) => (
              <tr
                key={i}
                className="cursor-pointer hover:bg-gray-50 transition-colors duration-200"
                onClick={() => {
                  if (IS_DISABLED) return;
                  handleClickOpenDialog(true, row);
                }}
              >
                <td className={bodyCellStyle}>{i + 1}</td>
                <td className={bodyCellStyle}>{row?.provider}</td>
                <td className={bodyCellStyle}>{row?.details}</td>
                <td className={numberCellStyle}>
                  {checkAndReplaceNumberWithZero(row?.no_of_site).toLocaleString()}
                </td>
                <td className={numberCellStyle}>
                  {checkAndReplaceNumberWithZero(row?.payment_term_in_days).toLocaleString()}
                </td>
                <td className={numberCellStyle}>
                  {row?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(row?.amount).toLocaleString()}
                </td>
                <td className={bodyCellStyle}>{moment(scenarioData?.project_start_date).format('DD MMM YYYY')}</td>
                <td className={bodyCellStyle}>{moment(scenarioData?.expected_rfs_date).format('DD MMM YYYY')}</td>
              </tr>
            ))}
            <tr className="bg-blue-50">
              <td colSpan={5} className={grandTotalStyle}>
                Total CAPEX Investment
              </td>
              <td className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                {projectData?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(
                  capexData?.reduce((prev, curr) => prev + curr?.amount, 0)
                ).toLocaleString()}
              </td>
              <td colSpan={2} className={grandTotalStyle} />
            </tr>
          </tbody>
        </table>
      </div>
      <div className="mt-10">
        <p className="text-lg font-bold">O&M SUMMARY</p>
      </div>
      <div className="flex flex-col gap-2 mt-6">
        <div className="overflow-x-auto mb-10">
          <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
            <thead>
              <tr>
                <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>
                  Provider
                </td>
                <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Details</td>
                {periodLabels.map((label, i) => (
                  <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
                ))}
                <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden text-right')}>Total ({projectData?.currency?.toUpperCase()})</td>
              </tr>
            </thead>
            <tbody>
              {(() => {
                const groups = {};
                capexData
                  ?.filter((o) => o.om_needed)
                  .forEach((o) => {
                    const key = `${o.provider || 'Unknown'}`;
                    if (!groups[key]) {
                      groups[key] = {
                        provider: o.provider || 'Unknown',
                        items: [],
                      };
                    }
                    groups[key].items.push(o);
                  });

                return Object.values(groups).map((group, groupIndex) => (
                  <Fragment key={groupIndex}>
                    {group.items.map((item, itemIndex) => (
                      <tr key={`${groupIndex}-${itemIndex}`}>
                        {itemIndex === 0 ? (
                          <td className={bodyCellStyle} rowSpan={group.items.length}>
                            {group.provider}
                          </td>
                        ) : null}
                        <td className={bodyCellStyle}>{item?.details}</td>
                        {convertToDisplayFormat(item, 'om_summary_by_year_array')?.map((p, j) => (
                          <td key={j} className={numberCellStyle}>
                            {checkAndReplaceNumberWithZero(p).toLocaleString()}
                          </td>
                        ))}
                        <td className={twMerge(numberCellStyle, 'bg-blue-50')}>
                          {checkAndReplaceNumberWithZero(
                            item?.om_summary_by_year_array?.reduce((a, b) => a + b, 0)
                          ).toLocaleString()}
                        </td>
                      </tr>
                    ))}

                    {/* Provider Subtotal Row */}
                    <tr className="bg-gray-100">
                      <td colSpan={2} className={subtotalStyle}>
                        Subtotal ({group.provider})
                      </td>
                      {(() => {
                        // Calculate yearly totals for this provider group
                        const yearlyTotals = capexData?.[0]?.om_summary_by_year_array?.map((_, yearIndex) => {
                          return group.items.reduce(
                            (sum, item) => sum + (item?.om_summary_by_year_array?.[yearIndex] || 0),
                            0
                          );
                        }) || [];

                        // Convert to monthly if needed
                        const displayTotals = isMonthlyDisplay
                          ? convertYearlyToMonthly(yearlyTotals, contractMonths, startGapMonths)
                          : yearlyTotals;

                        return displayTotals.map((total, periodIndex) => (
                          <td key={periodIndex} className={twMerge(subtotalStyle, 'text-right font-mono')}>
                            {checkAndReplaceNumberWithZero(total).toLocaleString()}
                          </td>
                        ));
                      })()}
                      <td className={twMerge(subtotalStyle, 'bg-blue-50 text-right font-mono')}>
                        {checkAndReplaceNumberWithZero(
                          group.items.reduce(
                            (sum, item) =>
                              sum + (item?.om_summary_by_year_array?.reduce((a, b) => a + b, 0) || 0),
                            0
                          )
                        ).toLocaleString()}
                      </td>
                    </tr>
                  </Fragment>
                ));
              })()}

              {/* Grand Total Row */}
              <tr className="bg-blue-50">
                <td colSpan={2} className={grandTotalStyle}>
                  Grand Total O&M
                </td>
                {(() => {
                  // Calculate yearly grand totals
                  const yearlyGrandTotals = capexData?.[0]?.om_summary_by_year_array?.map((_, yearIndex) => {
                    return capexData?.reduce(
                      (prev, curr) => prev + (curr?.om_summary_by_year_array?.[yearIndex] || 0),
                      0
                    );
                  }) || [];

                  // Convert to monthly if needed
                  const displayGrandTotals = isMonthlyDisplay
                    ? convertYearlyToMonthly(yearlyGrandTotals, contractMonths, startGapMonths)
                    : yearlyGrandTotals;

                  return displayGrandTotals.map((total, periodIndex) => (
                    <td key={periodIndex} className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                      {checkAndReplaceNumberWithZero(total).toLocaleString()}
                    </td>
                  ));
                })()}
                <td className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                  {checkAndReplaceNumberWithZero(
                    capexData?.reduce(
                      (prev, curr) =>
                        prev + curr?.om_summary_by_year_array?.reduce((a, b) => a + b, 0),
                      0
                    )
                  ).toLocaleString()}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className="mt-10">
          <p className="text-lg font-bold">DEPRECIATION SUMMARY</p>
        </div>
        <div className="overflow-x-auto mb-10">
          <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
            <thead>
              <tr>
                <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>
                  Provider
                </td>
                <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Details</td>
                {periodLabels.map((label, i) => (
                  <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
                ))}
                <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden text-right')}>Total ({projectData?.currency?.toUpperCase()})</td>
              </tr>
            </thead>
            <tbody>
              {(() => {
                const groups = {};
                capexData?.forEach((o) => {
                  const key = `${o.provider || 'Unknown'}`;
                  if (!groups[key]) {
                    groups[key] = {
                      provider: o.provider || 'Unknown',
                      items: [],
                    };
                  }
                  groups[key].items.push(o);
                });

                return Object.values(groups).map((group, groupIndex) => (
                  <Fragment key={groupIndex}>
                    {group.items.map((item, itemIndex) => (
                      <tr key={`${groupIndex}-${itemIndex}`}>
                        {itemIndex === 0 ? (
                          <td className={bodyCellStyle} rowSpan={group.items.length}>
                            {group.provider}
                          </td>
                        ) : null}
                        <td className={bodyCellStyle}>{item?.details}</td>
                        {convertToDisplayFormat(item, 'depreciation_summary_by_year_array')?.map((p, j) => (
                          <td key={j} className={numberCellStyle}>
                            {checkAndReplaceNumberWithZero(p).toLocaleString()}
                          </td>
                        ))}
                        <td className={twMerge(numberCellStyle, 'bg-blue-50')}>
                          {checkAndReplaceNumberWithZero(
                            item?.depreciation_summary_by_year_array?.reduce((a, b) => a + b, 0)
                          ).toLocaleString()}
                        </td>
                      </tr>
                    ))}

                    {/* Provider Subtotal Row */}
                    <tr className="bg-gray-100">
                      <td colSpan={2} className={subtotalStyle}>
                        Subtotal ({group.provider})
                      </td>
                      {(() => {
                        // Calculate yearly totals for this provider group
                        const yearlyTotals = capexData?.[0]?.depreciation_summary_by_year_array?.map((_, yearIndex) => {
                          return group.items.reduce(
                            (sum, item) => sum + (item?.depreciation_summary_by_year_array?.[yearIndex] || 0),
                            0
                          );
                        }) || [];

                        // Convert to monthly if needed
                        const displayTotals = isMonthlyDisplay
                          ? convertYearlyToMonthly(yearlyTotals, contractMonths, startGapMonths)
                          : yearlyTotals;

                        return displayTotals.map((total, periodIndex) => (
                          <td key={periodIndex} className={twMerge(subtotalStyle, 'text-right font-mono')}>
                            {checkAndReplaceNumberWithZero(total).toLocaleString()}
                          </td>
                        ));
                      })()}
                      <td className={twMerge(subtotalStyle, 'bg-blue-50 text-right font-mono')}>
                        {checkAndReplaceNumberWithZero(
                          group.items.reduce(
                            (sum, item) =>
                              sum +
                              (item?.depreciation_summary_by_year_array?.reduce(
                                (a, b) => a + b,
                                0
                              ) || 0),
                            0
                          )
                        ).toLocaleString()}
                      </td>
                    </tr>
                  </Fragment>
                ));
              })()}

              {/* Grand Total Row */}
              <tr className="bg-blue-50">
                <td colSpan={2} className={grandTotalStyle}>
                  Grand Total Depreciation
                </td>
                {(() => {
                  // Calculate yearly grand totals
                  const yearlyGrandTotals = capexData?.[0]?.depreciation_summary_by_year_array?.map((_, yearIndex) => {
                    return capexData?.reduce(
                      (prev, curr) => prev + (curr?.depreciation_summary_by_year_array?.[yearIndex] || 0),
                      0
                    );
                  }) || [];

                  // Convert to monthly if needed
                  const displayGrandTotals = isMonthlyDisplay
                    ? convertYearlyToMonthly(yearlyGrandTotals, contractMonths, startGapMonths)
                    : yearlyGrandTotals;

                  return displayGrandTotals.map((total, periodIndex) => (
                    <td key={periodIndex} className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                      {checkAndReplaceNumberWithZero(total).toLocaleString()}
                    </td>
                  ));
                })()}
                <td className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                  {checkAndReplaceNumberWithZero(
                    capexData?.reduce(
                      (prev, curr) =>
                        prev +
                        curr?.depreciation_summary_by_year_array?.reduce((a, b) => a + b, 0),
                      0
                    )
                  ).toLocaleString()}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Custom Table-based Attachments Section */}
      <div className="flex flex-col gap-2 mt-10">
        <div className="flex items-center justify-between">
          <p className="text-lg font-bold">SUPPORTING DOCUMENTS</p>
          {!IS_DISABLED && (
            <button
              type="button"
              className="flex items-center gap-1.5 whitespace-nowrap rounded-lg bg-gray-200 px-3 py-1.5 text-xs font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-300"
              onClick={() => document.getElementById('capex-file-upload').click()}
            >
              <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
              </svg>
              Upload Document
              <input
                id="capex-file-upload"
                type="file"
                className="hidden"
                onChange={async (event) => {
                  dispatch(setIsLoading(true));
                  try {
                    const response = await axios.post(
                      `${FA_ENDPOINT}/file/upload/capex/${scenarioId}`,
                      { file: event.target.files[0] },
                      {
                        headers: {
                          'Content-Type': 'multipart/form-data',
                        },
                      }
                    );

                    console.log('Upload response:', JSON.stringify(response.data, null, 2));
                    if (response.data?.data) {
                      console.log('Uploaded file object:', JSON.stringify(response.data.data, null, 2));
                    }

                    let statusVariant = 'error';
                    let message = 'Failed';
                    if (response.data.status === 'success' || response?.status === 201) {
                      statusVariant = 'success';
                      message = 'Success';
                      // Refresh the file list
                      fetchFileList();
                    }

                    enqueueSnackbar(message, {
                      variant: statusVariant,
                    });
                  } catch {
                    enqueueSnackbar('Something went wrong', {
                      variant: 'error',
                    });
                  }
                  dispatch(setIsLoading(false));
                  event.target.value = null;
                }}
              />
            </button>
          )}
        </div>
        
        <div className="overflow-x-auto mb-10">
          <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
            <thead>
              <tr>
                <td className={twMerge(headerCellStyle, 'w-[70%] text-left')}>
                  File Name
                </td>
                <td className={headerCellStyle}>
                  Actions
                </td>
              </tr>
            </thead>
          <tbody>
            {fileList.length > 0 ? (
              fileList.map((file, index) => {
                return (
                  <tr key={index} className="hover:bg-gray-50 transition-colors duration-200">
                    <td className={bodyCellStyle}>{file.file_name}</td>
                    <td className={bodyCellStyle}>
                      <div className="flex justify-center space-x-2">
                        <button
                          type="button"
                          className="rounded-md bg-blue-50 p-1.5 text-blue-500 hover:bg-blue-100"
                          onClick={() => downloadFile(file)}
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                          </svg>
                        </button>
                        {!IS_DISABLED && (
                          <button
                            type="button"
                            className="rounded-md bg-red-50 p-1.5 text-red-500 hover:bg-red-100"
                            onClick={() => {
                              setFocusedFile(file);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan="2" className={bodyCellStyle}>
                  No documents have been uploaded
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      </div>

      {/* CAPEX Add/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-fa text-center text-white">
          {!editModeDialog ? 'Add' : 'Edit'} CAPEX
        </DialogTitle>
        <DialogContent>
          <div className="flex">
            <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
              <TextInput
                name="provider"
                value={dialogData?.provider}
                placeholder="Provider"
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="details"
                value={dialogData?.details}
                placeholder="Details"
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="leg_a"
                value={dialogData?.leg_a}
                placeholder="Leg A"
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="leg_b"
                value={dialogData?.leg_b}
                placeholder="Leg B"
                onChange={handleDialogDataChange}
              />
              <SelectInput
                type="number"
                name="payment_term_in_days"
                value={dialogData?.payment_term_in_days}
                placeholder="Payment Term (Days)"
                options={[30, 45, 60]}
                onChange={handleDialogDataChange}
              />
              <TextInput
                type="number"
                name="no_of_site"
                value={dialogData?.no_of_site}
                placeholder="No. of Site"
                onChange={handleDialogDataChange}
              />
              <SelectInput
                name="currency"
                value={dialogData?.currency}
                placeholder="Currency"
                options={['myr', 'usd']}
                onChange={handleDialogDataChange}
              />
            </div>
            <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
              <TextInput
                type="number"
                name="amount"
                value={dialogData?.amount}
                placeholder="Amount"
                onChange={handleDialogDataChange}
              />
              <BinarySwitchInput
                name="om_needed"
                value={dialogData?.om_needed}
                placeholder="O&M Needed"
                onChange={e => {
                  handleDialogDataChange(e);
                  if (!e.target.value) {
                    setOmType('standard');
                  }
                }}
              />
              {dialogData?.om_needed && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 flex flex-col gap-2 mt-2">
                  <div className="flex flex-col gap-0.5">
                    <label className="text-xs font-medium flex items-center gap-1 mb-0">
                      O&M Calculation Type
                      <span className="font-semibold text-red-500">*</span>
                    </label>
                    <div className="flex gap-2 items-center">
                      <label className="flex items-center gap-1 cursor-pointer">
                        <input
                          type="radio"
                          name="om_type"
                          value="standard"
                          checked={omType === 'standard'}
                          onChange={() => { setOmType('standard'); setDialogData(prev => ({ ...prev, om_type: 'standard' })); }}
                          className="form-radio h-4 w-4 text-primary border-gray-300 focus:ring-2 focus:ring-primary"
                        />
                        <span className="text-xs">Standard</span>
                      </label>
                      <label className="flex items-center gap-1 cursor-pointer">
                        <input
                          type="radio"
                          name="om_type"
                          value="custom"
                          checked={omType === 'custom'}
                          onChange={() => { setOmType('custom'); setDialogData(prev => ({ ...prev, om_type: 'custom' })); }}
                          className="form-radio h-4 w-4 text-primary border-gray-300 focus:ring-2 focus:ring-primary"
                        />
                        <span className="text-xs">Custom</span>
                      </label>
                    </div>
                  </div>
                  {omType === 'standard' && (
                    <div className="flex flex-col gap-2 mt-1">
                      <TextInput
                        name="year1_rate"
                        value={projectData?.rates_locked_during_creation?.first_year_om}
                        placeholder="Year 1 Rate (%)"
                        disabled
                      />
                      <TextInput
                        name="subsequent_rate"
                        value={projectData?.rates_locked_during_creation?.om_annual_increment}
                        placeholder="Subsequent Year Rate (%)"
                        disabled
                      />
                    </div>
                  )}
                  {omType === 'custom' && (
                    <div className="flex flex-col gap-2 mt-1">
                      <TextInput
                        type="number"
                        name="custom_year1_rate"
                        value={dialogData?.custom_year1_rate || ''}
                        placeholder="Year 1 Rate (%)"
                        onChange={handleDialogDataChange}
                      />
                      <TextInput
                        type="number"
                        name="custom_subsequent_rate"
                        value={dialogData?.custom_subsequent_rate || ''}
                        placeholder="Subsequent Year Rate (%) (optional)"
                        onChange={handleDialogDataChange}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete', dialogData)}
                  className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
                >
                  Delete
                </button>
              )}
            </div>

            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post', dialogData)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                >
                  Create
                </button>
              )}

              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put', dialogData)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                >
                  Save
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle className="bg-fa text-center text-white">
          Delete Document?
        </DialogTitle>
        <div className="flex flex-col gap-4 p-4">
          <p className="text-sm">Are you sure you want to delete "{focusedFile?.file_name || focusedFile?.name || 'this file'}"?</p>
        </div>
        <div className="flex justify-between gap-4 p-4">
          <button type="button" onClick={() => setDeleteDialogOpen(false)} className="p-2">
            Cancel
          </button>
          <button
            type="button"
            onClick={async () => {
              dispatch(setIsLoading(true));
              try {
                const response = await axios.delete(`${FA_ENDPOINT}/file/${focusedFile?.id}`);

                let statusVariant = 'error';
                let message = 'Failed';
                if (response.data.status === 'success' || response?.status === 204) {
                  statusVariant = 'success';
                  message = 'Success';
                  // Refresh the file list
                  fetchFileList();
                }

                enqueueSnackbar(message, {
                  variant: statusVariant,
                });
              } catch (error) {
                console.error('Error deleting file:', error);
                enqueueSnackbar('Something went wrong', { variant: 'error' });
              }
              setDeleteDialogOpen(false);
              dispatch(setIsLoading(false));
            }}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500/50"
          >
            Delete
          </button>
        </div>
      </Dialog>
      <ScreenshotDialog
        fileName={`CAPEX-${projectData?.name}-${scenarioData?.name}.pdf`}
        isLandscape
      >
        <div className="flex w-full flex-col gap-4 p-4">
          {/* CAPEX Details Table */}
          <div className="bg-primary w-full text-center text-white">CAPEX DETAILS</div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  <td className="bg-fa w-[30px] border border-white px-4 text-center text-xs text-white">
                    No.
                  </td>
                  {[
                    'Provider',
                    'Details',
                    'No. of Site',
                    'Payment Term (Days)',
                    `Amount`,
                    'Start Date (from Scenario)',
                    'RFS Date (from Scenario)',
                  ].map((label, i) => (
                    <td
                      key={i}
                      className="bg-fa w-[150px] border border-white px-4 text-center text-xs text-white"
                    >
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {capexData.map((row, i) => (
                  <tr key={i} className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8]">
                    <td className={bodyCellStyle}>{i + 1}</td>
                    <td className={bodyCellStyle}>{row?.provider}</td>
                    <td className={bodyCellStyle}>{row?.details}</td>
                    <td className={bodyCellStyle}>{row?.no_of_site}</td>
                    <td className={bodyCellStyle}>{row?.payment_term_in_days}</td>
                    <td className={bodyCellStyle}>
                      {row?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(row?.amount)}
                    </td>
                    <td className={bodyCellStyle}>{scenarioData?.expected_rfs_date}</td>
                    <td className={bodyCellStyle}>{scenarioData?.project_start_date}</td>
                  </tr>
                ))}
                <tr className="bg-blue-50">
                  <td colSpan={5} className={grandTotalStyle}>
                    Total
                  </td>
                  <td className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                    {checkAndReplaceNumberWithZero(
                      capexData?.reduce((prev, curr) => prev + curr?.amount, 0)
                    ).toLocaleString()}
                  </td>
                  <td colSpan={2} className={grandTotalStyle} />
                </tr>
              </tbody>
            </table>
          </div>

          {/* O&M Summary Table */}
          <div className="bg-primary w-full text-center text-white mt-8">O&M SUMMARY</div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>
                    Provider
                  </td>
                  <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Details</td>
                  {periodLabels.map((label, i) => (
                    <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
                  ))}
                  <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden text-right')}>Total</td>
                </tr>
              </thead>
              <tbody>
                {(() => {
                  const groups = {};
                  capexData?.filter(item => item.om_needed)?.forEach((o) => {
                    const key = `${o.provider || 'Unknown'}`;
                    if (!groups[key]) {
                      groups[key] = {
                        provider: o.provider || 'Unknown',
                        items: [],
                      };
                    }
                    groups[key].items.push(o);
                  });

                  return Object.values(groups).map((group, groupIndex) => (
                    <Fragment key={groupIndex}>
                      {group.items.map((item, itemIndex) => (
                        <tr key={`${groupIndex}-${itemIndex}`}>
                          {itemIndex === 0 ? (
                            <td className={bodyCellStyle} rowSpan={group.items.length}>
                              {group.provider}
                            </td>
                          ) : null}
                          <td className={bodyCellStyle}>{item?.details}</td>
                          {convertToDisplayFormat(item, 'om_summary_by_year_array')?.map((p, j) => (
                            <td key={j} className={numberCellStyle}>
                              {checkAndReplaceNumberWithZero(p).toLocaleString()}
                            </td>
                          ))}
                          <td className={twMerge(numberCellStyle, 'bg-blue-50')}>
                            {checkAndReplaceNumberWithZero(
                              item?.om_summary_by_year_array?.reduce((a, b) => a + b, 0)
                            ).toLocaleString()}
                          </td>
                        </tr>
                      ))}

                      {/* Provider Subtotal Row */}
                      <tr className="bg-gray-100">
                        <td colSpan={2} className={subtotalStyle}>
                          Subtotal ({group.provider})
                        </td>
                        {(() => {
                          // Calculate yearly totals for this provider group
                          const yearlyTotals = group.items[0]?.om_summary_by_year_array?.map((_, yearIndex) => {
                            return group.items.reduce(
                              (sum, item) => sum + (item?.om_summary_by_year_array?.[yearIndex] || 0),
                              0
                            );
                          }) || [];

                          // Convert to monthly if needed
                          const displayTotals = isMonthlyDisplay
                            ? convertYearlyToMonthly(yearlyTotals, contractMonths, startGapMonths)
                            : yearlyTotals;

                          return displayTotals.map((total, periodIndex) => (
                            <td key={periodIndex} className={twMerge(subtotalStyle, 'text-right font-mono')}>
                              {checkAndReplaceNumberWithZero(total).toLocaleString()}
                            </td>
                          ));
                        })()}
                        <td className={twMerge(subtotalStyle, 'bg-blue-50 text-right font-mono')}>
                          {checkAndReplaceNumberWithZero(
                            group.items.reduce(
                              (prev, curr) =>
                                prev + curr?.om_summary_by_year_array?.reduce((a, b) => a + b, 0),
                              0
                            )
                          ).toLocaleString()}
                        </td>
                      </tr>
                    </Fragment>
                  ));
                })()}

                <tr className="bg-blue-50">
                  <td colSpan={2} className={grandTotalStyle}>
                    Grand Total O&M
                  </td>
                  {(() => {
                    // Calculate yearly grand totals
                    const yearlyGrandTotals = capexData?.[0]?.om_summary_by_year_array?.map((_, yearIndex) => {
                      return capexData?.reduce(
                        (prev, curr) => prev + (curr?.om_summary_by_year_array?.[yearIndex] || 0),
                        0
                      );
                    }) || [];

                    // Convert to monthly if needed
                    const displayGrandTotals = isMonthlyDisplay
                      ? convertYearlyToMonthly(yearlyGrandTotals, contractMonths, startGapMonths)
                      : yearlyGrandTotals;

                    return displayGrandTotals.map((total, periodIndex) => (
                      <td key={periodIndex} className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                        {checkAndReplaceNumberWithZero(total).toLocaleString()}
                      </td>
                    ));
                  })()}
                  <td className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                    {checkAndReplaceNumberWithZero(
                      capexData?.reduce(
                        (prev, curr) =>
                          prev + (curr?.om_summary_by_year_array?.reduce((a, b) => a + b, 0) || 0),
                        0
                      )
                    ).toLocaleString()}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          {/* Depreciation Summary Table */}
          <div className="bg-primary w-full text-center text-white mt-8">DEPRECIATION SUMMARY</div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>
                    Provider
                  </td>
                  <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Details</td>
                  {periodLabels.map((label, i) => (
                    <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
                  ))}
                  <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden text-right')}>Total</td>
                </tr>
              </thead>
              <tbody>
                {(() => {
                  const groups = {};
                  capexData?.forEach((o) => {
                    const key = `${o.provider || 'Unknown'}`;
                    if (!groups[key]) {
                      groups[key] = {
                        provider: o.provider || 'Unknown',
                        items: [],
                      };
                    }
                    groups[key].items.push(o);
                  });

                  return Object.values(groups).map((group, groupIndex) => (
                    <Fragment key={groupIndex}>
                      {group.items.map((item, itemIndex) => (
                        <tr key={`${groupIndex}-${itemIndex}`}>
                          {itemIndex === 0 ? (
                            <td className={bodyCellStyle} rowSpan={group.items.length}>
                              {group.provider}
                            </td>
                          ) : null}
                          <td className={bodyCellStyle}>{item?.details}</td>
                          {convertToDisplayFormat(item, 'depreciation_summary_by_year_array')?.map((p, j) => (
                            <td key={j} className={numberCellStyle}>
                              {checkAndReplaceNumberWithZero(p).toLocaleString()}
                            </td>
                          ))}
                          <td className={twMerge(numberCellStyle, 'bg-blue-50')}>
                            {checkAndReplaceNumberWithZero(
                              item?.depreciation_summary_by_year_array?.reduce((a, b) => a + b, 0)
                            ).toLocaleString()}
                          </td>
                        </tr>
                      ))}

                      {/* Subtotal row for each provider group */}
                      <tr className="bg-gray-50">
                        <td colSpan={2} className={subtotalStyle}>
                          Subtotal ({group.provider})
                        </td>
                        {(() => {
                          // Calculate yearly totals for this provider group
                          const yearlyTotals = capexData?.[0]?.depreciation_summary_by_year_array?.map((_, yearIndex) => {
                            return group.items.reduce(
                              (sum, item) => sum + (item?.depreciation_summary_by_year_array?.[yearIndex] || 0),
                              0
                            );
                          }) || [];

                          // Convert to monthly if needed
                          const displayTotals = isMonthlyDisplay
                            ? convertYearlyToMonthly(yearlyTotals, contractMonths, startGapMonths)
                            : yearlyTotals;

                          return displayTotals.map((total, periodIndex) => (
                            <td key={periodIndex} className={twMerge(subtotalStyle, 'text-right font-mono')}>
                              {checkAndReplaceNumberWithZero(total).toLocaleString()}
                            </td>
                          ));
                        })()}
                        <td className={twMerge(subtotalStyle, 'bg-blue-50 text-right font-mono')}>
                          {checkAndReplaceNumberWithZero(
                            group.items.reduce(
                              (sum, item) =>
                                sum +
                                (item?.depreciation_summary_by_year_array?.reduce(
                                  (a, b) => a + b,
                                  0
                                ) || 0),
                              0
                            )
                          ).toLocaleString()}
                        </td>
                      </tr>
                    </Fragment>
                  ));
                })()}

                <tr className="bg-blue-50">
                  <td colSpan={2} className={grandTotalStyle}>
                    Grand Total Depreciation
                  </td>
                  {(() => {
                    // Calculate yearly grand totals
                    const yearlyGrandTotals = capexData?.[0]?.depreciation_summary_by_year_array?.map((_, yearIndex) => {
                      return capexData?.reduce(
                        (prev, curr) => prev + (curr?.depreciation_summary_by_year_array?.[yearIndex] || 0),
                        0
                      );
                    }) || [];

                    // Convert to monthly if needed
                    const displayGrandTotals = isMonthlyDisplay
                      ? convertYearlyToMonthly(yearlyGrandTotals, contractMonths, startGapMonths)
                      : yearlyGrandTotals;

                    return displayGrandTotals.map((total, periodIndex) => (
                      <td key={periodIndex} className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                        {checkAndReplaceNumberWithZero(total).toLocaleString()}
                      </td>
                    ));
                  })()}
                  <td className={twMerge(grandTotalStyle, 'text-right font-mono')}>
                    {checkAndReplaceNumberWithZero(
                      capexData?.reduce(
                        (prev, curr) =>
                          prev +
                          curr?.depreciation_summary_by_year_array?.reduce((a, b) => a + b, 0),
                        0
                      )
                    ).toLocaleString()}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

        </div>
      </ScreenshotDialog>

      {/* Document Viewer Dialog */}
      <Dialog
        open={query.viewAttachmentDialogOpen === 'true'}
        fullScreen
        onClose={() => setParam({ viewAttachmentDialogOpen: 'false' })}
      >
        <DocViewer
          documents={[
            {
              uri: pdfUrl,
            },
          ]}
          config={{
            header: {
              disableHeader: true,
              disableFileName: true,
              retainURLParams: false,
            },
            pdfVerticalScrollByDefault: true,
          }}
          pluginRenderers={DocViewerRenderers}
        />
      </Dialog>
    </>
  );
};

AllCapex.propTypes = {
  scenarioId: PropTypes.string,
  showExportButton: PropTypes.bool,
  onDataLoaded: PropTypes.func,
  exportMode: PropTypes.bool,
};

export default AllCapex;
