import { Fragment } from 'react';
import { twMerge } from 'tailwind-merge';
import { getDisplayMode, generatePeriodLabels } from '../../../utils/faDisplayUtils';

// Safe number check function
const safeNumber = (value) => {
  if (value === null || value === undefined || Number.isNaN(value)) {
    return 0;
  }
  return Number(value);
};

// Safe array sum function
const safeArraySum = (array) => {
  if (!Array.isArray(array) || array.length === 0) {
    return 0;
  }
  return array.reduce((sum, value) => sum + safeNumber(value), 0);
};

// Utility to format numbers with thousand separators and 0 decimals
const formatNumber = (num) => {
  if (isNaN(num)) return '-';
  return Number(num).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
};

const RevenueSummaryTable = ({ revenueData, scenarioData }) => {
  // Modern, minimalist, Notion-like styling
  const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
  const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';
  const numberCellStyle = twMerge(bodyCellStyle, 'text-right font-mono');
  const numberCellStyleBold = twMerge(bodyCellStyle, 'text-right font-bold text-gray-800');
  const numberCellStyleTotal = twMerge(bodyCellStyle, 'bg-blue-50 text-right font-bold text-blue-900 border-b border-blue-200');

  // Ensure revenueData is an array
  const safeRevenueData = Array.isArray(revenueData) ? revenueData : [];

  // Always use yearly display mode
  const { isMonthlyDisplay, totalPeriods, contractMonths, startGapMonths } = getDisplayMode(scenarioData);

  // Generate period labels (always Y0, Y1, etc.)
  const periodLabels = generatePeriodLabels(false, totalPeriods);

  // Log display mode for debugging
  console.log('Revenue Summary display mode (always yearly):', {
    totalPeriods,
    contractMonths,
    startGapMonths,
    contract_period: scenarioData?.contract_period_in_month
  });

  return (
    <div className="flex flex-col gap-2 mt-8">
      <div className="flex items-center justify-between">
        <p className="text-lg font-bold">Revenue - Summary</p>
      </div>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
          <thead>
            <tr>
              <td className={twMerge(headerCellStyle, 'text-center')}>Description</td>
              <td className={twMerge(headerCellStyle, 'text-center')}>Details</td>
              {/* Use period labels based on display mode */}
              {periodLabels.map((label, i) => (
                <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
              ))}
              <td className={twMerge(headerCellStyle, 'text-right')}>Total</td>
            </tr>
          </thead>
          <tbody>

            {safeRevenueData.map((o, i) => (
              <Fragment key={i}>
                {/* Process data for display */}
                {(() => {
                  // Get yearly arrays from the data
                  const yearlyOtcArray = o?.revenue_otc_summary_by_year_array || [];
                  const yearlyAmortisedArray = o?.revenue_otc_amortised_summary_by_year_array || [];
                  const yearlyMrcArray = o?.revenue_mrc_summary_by_year_array || [];

                  // Ensure arrays match periodLabels length
                  const ensureCorrectLength = (array) => {
                    if (!array || array.length === 0) {
                      return Array(totalPeriods).fill(0);
                    }
                    // Pad with zeros if array is shorter than required
                    if (array.length < totalPeriods) {
                      return [...array, ...Array(totalPeriods - array.length).fill(0)];
                    }
                    // Truncate if array is longer than required
                    return array.slice(0, totalPeriods);
                  };

                  const displayOtcArray = ensureCorrectLength(yearlyOtcArray);
                  const displayAmortisedArray = ensureCorrectLength(yearlyAmortisedArray);
                  const displayMrcArray = ensureCorrectLength(yearlyMrcArray);

                  return (
                    <>
                      {/* OTC One Off Row */}
                      <tr>
                        <td className={bodyCellStyle} rowSpan={3}>
                          {o?.description || '-'}
                        </td>
                        <td className={bodyCellStyle}>OTC One Off</td>
                        {displayOtcArray.map((p, j) => (
                          <td key={j} className={numberCellStyle}>{formatNumber(safeNumber(p))}</td>
                        ))}
                        <td className={numberCellStyleTotal}>{formatNumber(safeArraySum(yearlyOtcArray))}</td>
                      </tr>

                      {/* OTC Amortised Row */}
                      <tr>
                        <td className={bodyCellStyle}>OTC Amortised</td>
                        {displayAmortisedArray.map((p, j) => (
                          <td key={j} className={numberCellStyle}>{formatNumber(safeNumber(p))}</td>
                        ))}
                        <td className={numberCellStyleTotal}>{formatNumber(safeArraySum(yearlyAmortisedArray))}</td>
                      </tr>

                      {/* ARC Row */}
                      <tr>
                        <td className={bodyCellStyle}>ARC</td>
                        {displayMrcArray.map((p, j) => (
                          <td key={j} className={numberCellStyle}>{formatNumber(safeNumber(p))}</td>
                        ))}
                        <td className={numberCellStyleTotal}>{formatNumber(safeArraySum(yearlyMrcArray))}</td>
                      </tr>
                    </>
                  );
                })()}

                <tr className="bg-gray-50">
                  <td colSpan={2} className={twMerge(bodyCellStyle, 'text-center font-semibold text-gray-800 border-b border-gray-200')}>
                    Total
                  </td>
                  {(() => {
                    // Get yearly arrays from the data
                    const yearlyOtcArray = o?.revenue_otc_summary_by_year_array || [];
                    const yearlyAmortisedArray = o?.revenue_otc_amortised_summary_by_year_array || [];
                    const yearlyMrcArray = o?.revenue_mrc_summary_by_year_array || [];

                    // Ensure arrays match periodLabels length
                    const ensureCorrectLength = (array) => {
                      if (!array || array.length === 0) {
                        return Array(totalPeriods).fill(0);
                      }
                      if (array.length < totalPeriods) {
                        return [...array, ...Array(totalPeriods - array.length).fill(0)];
                      }
                      return array.slice(0, totalPeriods);
                    };

                    const displayOtcArray = ensureCorrectLength(yearlyOtcArray);
                    const displayAmortisedArray = ensureCorrectLength(yearlyAmortisedArray);
                    const displayMrcArray = ensureCorrectLength(yearlyMrcArray);

                    // Calculate totals for each period
                    return Array.from({ length: totalPeriods }, (_, periodIndex) => {
                      const otcValue = safeNumber(displayOtcArray[periodIndex]);
                      const amortisedValue = safeNumber(displayAmortisedArray[periodIndex]);
                      const mrcValue = safeNumber(displayMrcArray[periodIndex]);
                      const periodTotal = otcValue + amortisedValue + mrcValue;

                      return (
                        <td key={periodIndex} className={numberCellStyleBold}>{formatNumber(periodTotal)}</td>
                      );
                    });
                  })()}
                  <td className={numberCellStyleTotal}>{formatNumber(
                    safeArraySum(o?.revenue_otc_summary_by_year_array) +
                    safeArraySum(o?.revenue_otc_amortised_summary_by_year_array) +
                    safeArraySum(o?.revenue_mrc_summary_by_year_array)
                  )}</td>
                </tr>
              </Fragment>
            ))}

            <tr className="bg-blue-50">
              <td colSpan={2} className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200')}>
                Grand Total
              </td>
              {(() => {
                // Prepare arrays for grand totals
                const grandTotalArray = Array(totalPeriods).fill(0);

                // Calculate grand totals for each period
                safeRevenueData.forEach(entry => {
                  // Get yearly arrays from the data
                  const yearlyOtcArray = entry?.revenue_otc_summary_by_year_array || [];
                  const yearlyAmortisedArray = entry?.revenue_otc_amortised_summary_by_year_array || [];
                  const yearlyMrcArray = entry?.revenue_mrc_summary_by_year_array || [];

                  // Always use yearly arrays
                  const displayOtcArray = yearlyOtcArray;
                  const displayAmortisedArray = yearlyAmortisedArray;
                  const displayMrcArray = yearlyMrcArray;

                  // Add to grand totals
                  for (let i = 0; i < grandTotalArray.length; i++) {
                    grandTotalArray[i] +=
                      safeNumber(displayOtcArray[i]) +
                      safeNumber(displayAmortisedArray[i]) +
                      safeNumber(displayMrcArray[i]);
                  }
                });

                // Render the grand total cells
                return grandTotalArray.map((total, i) => (
                  <td key={i} className={numberCellStyleBold}>{formatNumber(total)}</td>
                ));
              })()}
              <td className={numberCellStyleTotal}>{formatNumber(
                safeRevenueData.reduce((prev, curr) => {
                  const mrc = safeArraySum(curr?.revenue_mrc_summary_by_year_array);
                  const otc = safeArraySum(curr?.revenue_otc_summary_by_year_array);
                  const amortised = safeArraySum(curr?.revenue_otc_amortised_summary_by_year_array);
                  return prev + mrc + otc + amortised;
                }, 0)
              )}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default RevenueSummaryTable;
