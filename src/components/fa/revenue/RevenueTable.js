import { twMerge } from 'tailwind-merge';
import { checkAndReplaceNumberWithZero } from '../../../utils/shared';

const RevenueTable = ({ revenueData, onRowClick, IS_DISABLED }) => {
  // Modern, minimalist, Notion-like styling
  const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
  const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
        <thead>
          <tr>
            <td className={twMerge(headerCellStyle, 'w-[30px]')}>
              No.
            </td>
            {[
              'Type',
              'Description',
              'Product',
              'Leg A',
              'Leg B',
              'Bandwidth',
              'RFS Date',
              'Contract End Date',
              'Payment Term (Days)',
              'Type Price',
              `Amount (OTC)`,
              `Amount (OTC Amortise)`,
              `Amount (MRC)`,
              `Total Contract Value`,
            ].map((label, i) => (
              <td
                key={i}
                className={twMerge(headerCellStyle, 'w-[150px]')}
              >
                {label}
              </td>
            ))}
          </tr>
        </thead>
        <tbody>
          {revenueData.map((row, i) => (
            <tr
              key={i}
              className="cursor-pointer hover:bg-gray-50 transition-colors duration-200"
              onClick={() => {
                if (IS_DISABLED) return;
                onRowClick(row);
              }}
            >
              <td className={bodyCellStyle}>{i + 1}</td>
              <td className={bodyCellStyle}>{row?.type}</td>
              <td className={bodyCellStyle}>{row?.description}</td>
              <td className={bodyCellStyle}>{row?.product}</td>
              <td className={bodyCellStyle}>{row?.leg_a}</td>
              <td className={bodyCellStyle}>{row?.leg_b}</td>
              <td className={bodyCellStyle}>
                {row?.bandwidth_value} {row?.bandwidth_unit}
              </td>
              <td className={bodyCellStyle}>{row?.rfs_date}</td>
              <td className={bodyCellStyle}>{row?.contract_end_date}</td>
              <td className={bodyCellStyle}>{row?.payment_term_in_days}</td>
              <td className={bodyCellStyle}>{row?.type_price?.toUpperCase()}</td>
              <td className={twMerge(bodyCellStyle, 'text-right font-mono')}>
                {row?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(row?.otc_amount).toLocaleString()}
              </td>
              <td className={twMerge(bodyCellStyle, 'text-right font-mono')}>
                {row?.currency?.toUpperCase()}{' '}
                {checkAndReplaceNumberWithZero(row?.otc_amortise).toLocaleString()}
              </td>
              <td className={twMerge(bodyCellStyle, 'text-right font-mono')}>
                {row?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(row?.mrc_amount).toLocaleString()}
              </td>
              <td className={twMerge(bodyCellStyle, 'text-right font-mono')}>
                {row?.currency?.toUpperCase()}{' '}
                {checkAndReplaceNumberWithZero(row?.total_contract_value).toLocaleString()}
              </td>
            </tr>
          ))}
          <tr className="bg-blue-50">
            <td colSpan={11} className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200')}>
              Total
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200 text-right font-mono')}>
              {checkAndReplaceNumberWithZero(
                revenueData?.reduce((prev, curr) => prev + curr?.otc_amount, 0)
              ).toLocaleString()}
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200 text-right font-mono')}>
              {checkAndReplaceNumberWithZero(
                revenueData?.reduce((prev, curr) => prev + curr?.otc_amortise, 0)
              ).toLocaleString()}
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200 text-right font-mono')}>
              {checkAndReplaceNumberWithZero(
                revenueData?.reduce((prev, curr) => prev + curr?.mrc_amount, 0)
              ).toLocaleString()}
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200 text-right font-mono')}>
              {checkAndReplaceNumberWithZero(
                revenueData?.reduce((prev, curr) => prev + curr?.total_contract_value, 0)
              ).toLocaleString()}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default RevenueTable; 