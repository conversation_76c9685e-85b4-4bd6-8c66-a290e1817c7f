// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useSelector, useDispatch } from 'react-redux';

// Mui
import { Switch } from '@mui/material';

// Packages
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import ScreenshotDialog from '../Shared/ScreenshotDialog';

// Others
import axios from '../../utils/axios';
import { checkAndReplaceNumberWithZero, toUpperCaseFirstLetter } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { FA_ENDPOINT } from '../../utils/fa';
import { useSnackbar } from '../Shared/snackbar';
import { setBreadCrumbsList } from '../../utils/store/simiReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { getDisplayMode, generatePeriodLabels, convertYearlyToMonthly } from '../../utils/faDisplayUtils';
import { FinancialTableSkeleton, LoadingSpinner } from './ui/LoadingStates';
import { NetworkError, DataError, CalculationError } from './ui/ErrorStates';

// Enhanced number formatting utility
const formatNumber = (number, options = {}) => {
  const { 
    currency = '', 
    isPercentage = false, 
    showCurrency = false,
    decimalPlaces = 0 
  } = options;
  
  if (!number || isNaN(number)) return '0';
  
  const formattedNumber = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  }).format(Math.abs(number));
  
  const sign = number < 0 ? '-' : '';
  const currencyPrefix = showCurrency && currency ? `${currency.toUpperCase()} ` : '';
  const percentageSuffix = isPercentage ? ' %' : '';
  
  return `${sign}${currencyPrefix}${formattedNumber}${percentageSuffix}`;
};

// Enhanced cell wrapper for consistent styling
const FormattedCell = ({ value, currency, isPercentage, showCurrency, className = "" }) => {
  const textColor = value < 0 ? 'text-red-600' : 'text-gray-900';
  
  return (
    <span className={`font-mono text-sm ${textColor} ${className}`}>
      {formatNumber(value, { currency, isPercentage, showCurrency })}
    </span>
  );
};

// Responsive Table Wrapper Component (reusable)
const ResponsiveTableWrapper = ({ children, className = "" }) => (
  <div className={`relative ${className}`}>
    {/* Scroll indicator shadows */}
    <div className="absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-white via-white/80 to-transparent pointer-events-none z-10 opacity-0 transition-opacity duration-200" />
    <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-white via-white/80 to-transparent pointer-events-none z-10 opacity-0 transition-opacity duration-200" />
    
    {/* Scrollable container */}
    <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100 hover:scrollbar-thumb-slate-400">
      <div className="min-w-full">
        {children}
      </div>
    </div>
    
    {/* Mobile scroll hint */}
    <div className="block md:hidden text-center text-xs text-gray-500 mt-2">
      ← Scroll horizontally to view all data →
    </div>
  </div>
);

const FinancialAnalysis = ({
  isSummary,
  showApproverEndorsementCell,
  scenarioId,
  showExportButton = true,
  exportMode = false,
  cellType = null,
}) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { projectData } = useSelector((state) => state.fa);
  const { query } = useRouter();
  const { projectId } = query;
  const { setParam } = useParamContext();

  const [scenarioData, setScenarioData] = useState({});
  const [approvalsData, setApprovalsData] = useState([]);
  const [capexData, setCapexData] = useState([]);
  const [opexData, setOpexData] = useState([]);
  const [revenueData, setRevenueData] = useState([]);
  const [providerCogsData, setProviderCogsData] = useState([]); // New state for Provider COGS
  const [endorsementSwitchIsDisabled, setEndorsementSwitchIsDisabled] = useState(false);
  const [isLoading, setIsComponentLoading] = useState(true);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Removed collapsible section states - now using smart connectors always visible

  // Determine display mode based on contract period
  const { isMonthlyDisplay, totalPeriods, contractMonths, periodType, startGapMonths } = getDisplayMode(scenarioData);

  // Generate period labels (M0, M1, etc. or Y0, Y1, etc.)
  const periodLabels = generatePeriodLabels(isMonthlyDisplay, totalPeriods);

  // Log display mode for debugging
  useEffect(() => {
    console.log('Financial Analysis display mode:', {
      isMonthlyDisplay,
      totalPeriods,
      contractMonths,
      startGapMonths,
      contract_period: scenarioData?.contract_period_in_month,
      periodLabels
    });
  }, [isMonthlyDisplay, totalPeriods, contractMonths, startGapMonths, scenarioData, periodLabels]);

  // Enhanced Table Styles
  const headerCellStyle =
    'whitespace-nowrap bg-slate-100 px-4 py-3 text-sm text-center text-slate-800 font-semibold border-b-2 border-slate-300';
  
  // Enhanced cell styles matching the first column design
  const getEnhancedCellStyle = (variant = 'default') => {
    const baseStyle = 'min-w-[200px] text-right py-3 text-sm whitespace-nowrap px-4 border-r border-slate-200 transition-colors duration-200 h-[50px] font-mono';
    switch (variant) {
      case 'contract':
        return twMerge(baseStyle, 'bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold text-base border-0 h-[60px] text-center font-sans');
      case 'approver':
        return twMerge(baseStyle, 'bg-amber-50 border-amber-200 text-amber-900 h-[70px] border-b-2 text-center font-sans');
      case 'header':
        return twMerge(baseStyle, 'bg-slate-100 font-semibold text-slate-800 border-b-2 border-slate-300 text-center font-sans');
      case 'section':
        return twMerge(baseStyle, 'bg-blue-50 font-bold text-blue-900 border-b border-blue-200 text-center font-sans');
      case 'subsection':
        return twMerge(baseStyle, 'bg-slate-50 font-semibold text-slate-700');
      case 'highlight':
        return twMerge(baseStyle, 'bg-emerald-50 font-semibold text-emerald-800');
      case 'expanded-item':
        return twMerge(baseStyle, 'bg-blue-50 text-blue-900 text-xs pl-8');
      default:
        return twMerge(baseStyle, 'bg-white text-slate-600 hover:bg-slate-50');
    }
  };
  
  const bodyCellStyle = 'whitespace-nowrap text-right py-3 text-sm border-r border-slate-200 transition-colors duration-200 font-mono';
  const bodyCellStyle2 = 'min-w-[200px]';
  const getTextNumberColor = (number) => (number < 0 ? 'text-red-600' : 'text-gray-900');

  // Smart connector styling functions
  const getSubsectionCellStyle = () => {
    return twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left font-bold text-gray-700');
  };

  const getSubitemCellStyle = () => {
    return twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left text-gray-600 text-sm border-l-4 border-blue-200');
  };

  const getSubitemValueCellStyle = () => {
    return twMerge(bodyCellStyle, 'bg-gray-50 text-gray-600 text-sm');
  };

  const getIndividualItemCellStyle = () => {
    return twMerge(bodyCellStyle, 'bg-blue-50 px-4 text-left text-blue-900 text-xs pl-8');
  };

  const getIndividualItemValueCellStyle = () => {
    return twMerge(bodyCellStyle, 'bg-blue-50 text-blue-900 text-xs');
  };

  // Enhanced number formatting for detailed view
  const formatDetailNumber = (number, options = {}) => {
    const { isPercentage = false, currency = '' } = options;
    return formatNumber(number, { isPercentage, currency });
  };

  // Others

  // Helper function to convert yearly data to monthly data for display
  const convertToDisplayFormat = (yearlyArray) => {
    if (!yearlyArray || !yearlyArray.length) {
      return [];
    }

    if (isMonthlyDisplay) {
      // Convert yearly data to monthly data, accounting for the gap between project start and RFS date
      return convertYearlyToMonthly(yearlyArray, contractMonths, startGapMonths);
    }

    // Return the original yearly data
    return yearlyArray;
  };

  const sumArrayByIndex = (dataArray, key) => {
    if (dataArray.length === 0) return { total: 0, array: [] };
    let resultArray = dataArray?.[0]?.[key];

    for (let i = 1; i < dataArray?.length; i += 1) {
      resultArray = resultArray?.map((item, j) => item + dataArray?.[i]?.[key]?.[j]);
    }

    const total = resultArray?.reduce((sum, item) => sum + item, 0);

    return { total, array: resultArray };
  };

  // Calculate yearly data first, then convert to display format if needed
  const calculateData = (dataArray, key) => {
    const { total, array } = sumArrayByIndex(dataArray, key);
    return {
      total,
      array: convertToDisplayFormat(array)
    };
  };

  const om = (() => {
    // Use the prorated O&M values from capexData directly
    const filteredCapexData = capexData?.filter((o) => o?.om_needed);
    const { total, array } = sumArrayByIndex(filteredCapexData, 'om_summary_by_year_array');
    return {
      total,
      array: convertToDisplayFormat(array)
    };
  })();

  // Calculate yearly data first, then convert to display format
  const depreciation = calculateData(capexData, 'depreciation_summary_by_year_array');
  const opex = calculateData(opexData, 'opex_summary_by_year_array');
  const revenueMrc = calculateData(revenueData, 'revenue_mrc_summary_by_year_array');
  const revenueOtc = calculateData(revenueData, 'revenue_otc_summary_by_year_array');
  const revenueOtcAmortised = calculateData(revenueData, 'revenue_otc_amortised_summary_by_year_array');

  // COGS calculation
  // Filter OPEX data to get only items where provider = "cogs"
  const cogs = (() => {
    // Filter OPEX data to get only COGS items
    const cogsItems = opexData?.filter(item => item?.provider?.toLowerCase() === 'cogs') || [];
    const { total, array } = sumArrayByIndex(cogsItems, 'opex_summary_by_year_array');
    return {
      total: total || 0,
      array: convertToDisplayFormat(array) || [],
      items: cogsItems || [],
    };
  })();

  // ROU calculation
  // Filter OPEX data to get only items where provider = "rou"
  const rou = (() => {
    // Filter OPEX data to get only ROU items
    const rouItems = opexData?.filter(item => item?.provider?.toLowerCase() === 'rou') || [];
    const { total, array } = sumArrayByIndex(rouItems, 'opex_summary_by_year_array');
    return {
      total: total || 0,
      array: convertToDisplayFormat(array) || [],
      items: rouItems || [],
    };
  })();

  // Regular OPEX calculation (excluding COGS and ROU)
  const regularOpex = (() => {
    // Filter OPEX data to exclude COGS and ROU items
    const regularOpexItems = opexData?.filter(
      item => item?.provider?.toLowerCase() !== 'cogs' && item?.provider?.toLowerCase() !== 'rou'
    ) || [];
    const { total, array } = sumArrayByIndex(regularOpexItems, 'opex_summary_by_year_array');
    return {
      total: total || 0,
      array: convertToDisplayFormat(array) || [],
      items: regularOpexItems || [],
    };
  })();
  const revenue = (() => {
    // Calculate yearly totals first
    const yearlyTotalArray = revenueMrc?.array?.map(
      (amount, i) => amount + revenueOtc?.array?.[i] + revenueOtcAmortised?.array[i]
    );

    // Convert to display format (monthly or yearly)
    const displayArray = convertToDisplayFormat(yearlyTotalArray);

    return {
      total: yearlyTotalArray.reduce((sum, amount) => sum + amount, 0),
      array: displayArray,
    };
  })();

  const usp = (() => {
    console.log('🔍 USP Debug Info:');
    console.log('- revenueData:', revenueData);
    console.log('- projectData?.rates_locked_during_creation?.usp:', projectData?.rates_locked_during_creation?.usp);
    console.log('- revenue.total:', revenue.total);
    
    // Check if ANY revenue item has USP enabled
    const hasUspNeeded = revenueData?.some(item => item?.usp_needed === true);
    console.log('- hasUspNeeded (any revenue item):', hasUspNeeded);
    
    if (!hasUspNeeded) {
      console.log('❌ USP = 0 because no revenue items have usp_needed enabled');
      return { total: 0, array: revenue.array.map(() => 0) };
    }
    
    const uspRate = projectData?.rates_locked_during_creation?.usp / 100;
    console.log('- USP Rate (decimal):', uspRate);
    
    if (!uspRate || uspRate === 0) {
      console.log('❌ USP = 0 because USP rate is not set or is 0');
      return { total: 0, array: revenue.array.map(() => 0) };
    }
    
    // Calculate USP based on revenue from items that have USP enabled
    const uspEnabledRevenue = (() => {
      const uspItems = revenueData?.filter(item => item?.usp_needed === true) || [];
      const { total: uspRevTotal, array: uspRevArray } = calculateData(uspItems, 'revenue_mrc_summary_by_year_array');
      const { total: uspOtcTotal, array: uspOtcArray } = calculateData(uspItems, 'revenue_otc_summary_by_year_array');
      const { total: uspOtcAmortisedTotal, array: uspOtcAmortisedArray } = calculateData(uspItems, 'revenue_otc_amortised_summary_by_year_array');
      
      const yearlyTotalArray = uspRevArray?.map(
        (amount, i) => amount + (uspOtcArray?.[i] || 0) + (uspOtcAmortisedArray?.[i] || 0)
      ) || [];
      
      const displayArray = convertToDisplayFormat(yearlyTotalArray);
      
      return {
        total: yearlyTotalArray.reduce((sum, amount) => sum + amount, 0),
        array: displayArray,
      };
    })();
    
    const temp = uspEnabledRevenue.array.map((amount) => amount * uspRate);
    const total = temp?.reduce((curr, prev) => curr + prev, 0);
    
    console.log('✅ USP Calculated:', { total, array: temp, uspEnabledRevenue });
    
    return {
      total,
      array: temp,
    };
  })();

  const stampingFees = (() => {
    if (!projectData?.stamping_fees_needed) {
      return { total: 0, array: revenue.array.map(() => 0) };
    }
    
    const stampingFeesRate = projectData?.rates_locked_during_creation?.stamping_fees / 100;
    
    if (!stampingFeesRate || stampingFeesRate === 0) {
      return { total: 0, array: revenue.array.map(() => 0) };
    }
    
    const { total, array } = revenue;
    return {
      total: total * stampingFeesRate,
      array: array.map((amount, i) => (i === 0 ? total * stampingFeesRate : 0)),
    };
  })();

  const totalOpex = (() => {
    const totalArray = stampingFees?.array.map(
      (amount, i) =>
        amount + (om?.array?.[i] || 0) + (regularOpex?.array?.[i] || 0) + (usp?.array?.[i] || 0)
    );
    return {
      total: totalArray.reduce((sum, amount) => sum + amount, 0),
      array: totalArray,
    };
  })();

  const ebitda = {
    total: revenue.total - totalOpex.total - cogs.total,
    array: revenue.array.map((amount, i) => amount - totalOpex.array[i] - (cogs?.array?.[i] || 0)),
  };

  const ebit = {
    total: ebitda.total - depreciation.total - rou.total,
    array: ebitda.array.map((amount, i) => amount - (depreciation.array[i] || 0) - (rou?.array?.[i] || 0)),
  };

  const ebitMargin = {
    total: (ebit.total / revenue.total) * 100,
  };

  const taxPercentage = (() => {
    const taxRates = {
      'TM Tech': projectData?.rates_locked_during_creation?.malaysia_tax,
      'TM HK': projectData?.rates_locked_during_creation?.hong_kong_tax,
      'TM SG': projectData?.rates_locked_during_creation?.singapore_tax,
      'TM US': projectData?.rates_locked_during_creation?.united_state_tax,
    };
    return (taxRates[projectData?.entity] || 0) / 100;
  })();

  const tax = {
    total: ebit.total * taxPercentage,
    array: ebit.array.map((amount) => amount * taxPercentage),
  };

  const pat = {
    total: ebit.total - tax.total,
    array: ebit.array.map((amount, i) => amount - tax.array[i]),
  };

  const patMargin = {
    total: pat.total / revenue.total,
  };

  const netIncome = pat;

  const addBackDepreciation = {
    total: depreciation.total + rou.total,
    array: depreciation.array.map((amount, i) => amount + (rou?.array?.[i] || 0)),
  };

  const addBackOtcAmortised = (() => {
    const { array } = revenueOtcAmortised;
    const temp = array?.map((amount, i) => {
      if (i === 0) {
        return array?.slice(1)?.reduce((a, b) => a + b, 0);
      }
      return -amount;
    });
    return { total: temp?.reduce((a, b) => a + b, 0), array: temp };
  })();

  const accountReceivableDays = revenueData?.[0]?.payment_term_in_days || 30;

  const date = capexData?.[0]?.expected_rfs_or_handover_date
    ? capexData?.[0]?.expected_rfs_or_handover_date
    : revenueData?.[0]?.rfs_date;

  const noOfDaysArray = (() => {
    const firstYearNoOfDays =
      moment(date)
        ?.endOf('year')
        ?.diff(moment(date)?.startOf('month')?.subtract(1, 'days'), 'months') * 30;

    const lastYearNoOfDays =
      (12 -
        moment(date)
          ?.add(scenarioData?.contract_period_in_month, 'months')
          ?.endOf('year')
          ?.diff(
            moment(date)
              ?.add(scenarioData?.contract_period_in_month, 'months')
              ?.subtract(1, 'days'),
            'months'
          )) *
      30;

    const temp = [];
    for (let i = 0; i < revenue.array.length; i += 1) {
      if (i === 0) temp.push(firstYearNoOfDays);
      else if (i === revenue.array.length - 1) temp.push(lastYearNoOfDays);
      else temp.push(365);
    }
    return temp;
  })();

  const accountReceivableClosingBalance = (() => {
    const days = accountReceivableDays;
    const { array } = revenue;
    const otcArray = revenueOtc?.array;
    const otcAmortisedArray = revenueOtcAmortised?.array;
    return {
      array: array.map((amount, i) => {
        if (i === 0) {
          const temp = otcArray?.slice(1)?.reduce((a, b) => a + b, 0);
          const temp2 = otcAmortisedArray?.slice(1)?.reduce((a, b) => a + b, 0);
          return (days / noOfDaysArray[i]) * (amount + temp + temp2);
        }
        return (days / noOfDaysArray[i]) * (amount - otcArray[i] - otcAmortisedArray[i]);
      }),
    };
  })();

  const accountReceivableOpeningBalance = (() => {
    const closingBalanceArray = accountReceivableClosingBalance.array;
    return {
      array: [0, ...closingBalanceArray.slice(0, -1)],
    };
  })();

  const accountReceivableMovement = (() => {
    const openingBalanceArray = accountReceivableOpeningBalance.array;
    const closingBalanceArray = accountReceivableClosingBalance.array;
    return {
      array: openingBalanceArray.map((amount, i) => amount - closingBalanceArray[i]),
    };
  })();

  const accountPayableDays =
    capexData?.[0]?.payment_term_in_days || opexData?.[0]?.payment_term_in_days || 30;

  const opexInternalCost = {
    array: totalOpex.array.map(() => 0),
  };

  const accountPayableClosingBalance = (() => {
    const totalOpexArray = totalOpex.array;
    const internalCostArray = opexInternalCost.array;
    return {
      array: totalOpexArray.map(
        (amount, i) => (accountPayableDays / noOfDaysArray[i]) * (amount - internalCostArray[i])
      ),
    };
  })();

  const accountPayableOpeningBalance = (() => {
    const closingBalanceArray = accountPayableClosingBalance.array;
    return {
      array: [0, ...closingBalanceArray.slice(0, -1)],
    };
  })();

  const accountPayableMovement = (() => {
    const openingBalanceArray = accountPayableOpeningBalance.array;
    const closingBalanceArray = accountPayableClosingBalance.array;
    return {
      array: openingBalanceArray.map((amount, i) => closingBalanceArray[i] - amount),
    };
  })();

  const nettWorkingCapital = (() => {
    const receivableMovementArray = accountReceivableMovement.array;
    const payableMovementArray = accountPayableMovement.array;
    const nettWorkingCapitalArray = receivableMovementArray.map(
      (amount, i) => amount + payableMovementArray[i]
    );
    return {
      total: nettWorkingCapitalArray.reduce((sum, amount) => sum + amount, 0),
      array: nettWorkingCapitalArray,
    };
  })();

  const nettCashflowAfterContractEnd = (() => {
    const receivableClosingBalanceArray = accountReceivableClosingBalance.array;
    const payableClosingBalanceArray = accountPayableClosingBalance.array;
    const cashflowArray = [];
    for (let i = 0; i < receivableClosingBalanceArray?.length; i += 1) {
      if (i === receivableClosingBalanceArray?.length - 1)
        cashflowArray?.push(receivableClosingBalanceArray[i] - payableClosingBalanceArray[i]);
      else cashflowArray?.push(0);
    }
    return {
      total: cashflowArray.reduce((sum, amount) => sum + amount, 0),
      array: cashflowArray,
    };
  })();

  const cashFlowFromOperation = (() => {
    const totalArray = netIncome.array.map(
      (amount, i) =>
        amount +
        (addBackDepreciation.array[i] || 0) +
        (addBackOtcAmortised.array[i] || 0) +
        (nettWorkingCapital.array[i] || 0) +
        (nettCashflowAfterContractEnd.array[i] || 0)
    );
    return {
      total:
        netIncome.total +
        addBackDepreciation.total +
        addBackOtcAmortised.total +
        nettWorkingCapital.total +
        nettCashflowAfterContractEnd.total,
      array: totalArray,
    };
  })();

  const capex = (() => {
    const array = [];
    const total = capexData?.reduce((sum, curr) => sum + curr?.amount, 0);
    for (let i = 0; i < om.array.length; i += 1) {
      if (i === 0) array?.push(total);
      else array?.push(0);
    }

    if (array?.length === 0) revenue?.array?.forEach(() => array?.push(0));

    return {
      total,
      array,
    };
  })();

  const freeCashFlow = (() => {
    const totalArray = cashFlowFromOperation.array.map((amount, i) => amount - capex.array[i]);
    return {
      total: cashFlowFromOperation.total - capex.total,
      array: totalArray,
    };
  })();

  const openingCash = (() => {
    const freeCashFlowArray = freeCashFlow.array;
    const openingCashArray = [0];
    for (let i = 1; i < freeCashFlowArray.length; i += 1) {
      openingCashArray.push(openingCashArray[i - 1] + freeCashFlowArray[i - 1]);
    }
    return { array: openingCashArray };
  })();

  const closingCash = (() => {
    const freeCashFlowArray = freeCashFlow.array;
    const openingCashArray = openingCash.array;
    return {
      array: freeCashFlowArray.map((amount, i) => amount + openingCashArray[i]),
    };
  })();

  const xnpvCalculator = (rate, cashFlows, dateStrings) => {
    let npv = 0;
    const initialDate = new Date(dateStrings[0]);
    cashFlows.forEach((cashFlow, i) => {
      const date_ = new Date(dateStrings[i]);
      const time = (date_ - initialDate) / (1000 * 60 * 60 * 24 * 365);
      npv += cashFlow / (1 + rate) ** time;
    });
    return npv;
  };

  const dateArray = (() => {
    const startDate = moment(date);
    return revenue?.array.map((_, i, array) => {
      if (i === 0)
        return moment(capexData?.[0]?.project_start_or_expected_gr_or_capex_released_date).format(
          'YYYY-MM-DD'
        );
      if (i === array.length - 1)
        return startDate
          .clone()
          .add(scenarioData?.contract_period_in_month, 'months')
          .subtract(1, 'days')
          .format('YYYY-MM-DD');
      return startDate
        .clone()
        .add(i * 12, 'months')
        .endOf('year')
        .format('YYYY-MM-DD');
    });
  })();

  const xnpv = (() => {
    const freeCashFlowArray = freeCashFlow.array;
    const npvArray = freeCashFlowArray.map((_, i) =>
      xnpvCalculator(
        projectData?.rates_locked_during_creation?.cost_of_capital / 100,
        freeCashFlowArray.slice(0, i + 1),
        dateArray.slice(0, i + 1)
      )
    );
    return {
      total: npvArray[npvArray.length - 1],
      array: npvArray,
    };
  })();

  const goodXnpv = xnpv?.total > 1;

  const mirrCalculator = (cashFlows, financeRate, reinvestRate) => {
    const n = cashFlows.length;

    let npvPositive = 0;
    let npvNegative = 0;

    for (let t = 0; t < n; t += 1) {
      if (cashFlows[t] > 0) {
        // eslint-disable-next-line
        npvPositive += cashFlows[t] / Math.pow(1 + reinvestRate, t);
      } else {
        // eslint-disable-next-line
        npvNegative += cashFlows[t] / Math.pow(1 + financeRate, t);
      }
    }
    // eslint-disable-next-line
    return Math.pow(-npvPositive / npvNegative, 1 / (n - 1)) * (1 + reinvestRate) - 1;
  };

  const irr = {
    total:
      mirrCalculator(
        freeCashFlow.array,
        projectData?.rates_locked_during_creation?.cost_of_capital / 100,
        projectData?.rates_locked_during_creation?.reinvestment / 100
      ) * 100,
  };

  const goodIrr = irr?.total > 14;

  const lookUp = (valueToLookFor, arrayToLookup, arrayToReference) => {
    let indexOfClosestToValueToLookFor = 0;
    let currentSmallestDifference = Math.abs(valueToLookFor - arrayToLookup[0]);

    for (let i = 1; i < arrayToLookup.length; i += 1) {
      const currentDifference = Math.abs(valueToLookFor - arrayToLookup[i]);
      if (currentDifference < currentSmallestDifference) {
        currentSmallestDifference = currentDifference;
        indexOfClosestToValueToLookFor = i;
      }
    }
    return arrayToReference[indexOfClosestToValueToLookFor];
  };

  const paybackPeriodInYears = (() => {
    const freeCashFlowArray = freeCashFlow.array;
    const closingCashArray = closingCash.array;
    const temp = closingCashArray?.map((o, i) => i - o / freeCashFlowArray[i + 1]);
    return {
      total: lookUp(0, closingCashArray, temp),
    };
  })();

  const dppInYears = (() => {
    const presentValueNetCashFlowArray = freeCashFlow.array?.map(
      (o, i) =>
        // eslint-disable-next-line
        o * Math.pow(1 / (1 + projectData?.rates_locked_during_creation?.cost_of_capital / 100), i)
    );

    const npvArray = xnpv.array;

    const temp = npvArray?.map((o, i) => i - o / presentValueNetCashFlowArray[i + 1]);
    return {
      total: lookUp(0, npvArray, temp),
    };
  })();

  const humanizeNumber = (number) => {
    if (number === Infinity || number === -Infinity || number < 0 || String(number) === 'NaN')
      return 'N/A';
    return checkAndReplaceNumberWithZero(number, 2);
  };

  const faDetailsPart = (
    <ResponsiveTableWrapper>
      <table className="min-w-full bg-white text-black">
        <tbody>
          <tr>
            <td className="text-center text-xs" />
            <td className="text-center text-xs" />
            {dateArray?.map((o, i) => (
              <td key={i} className="text-center text-xs">
                {moment(o, 'YYYY-MM-DD')?.format('DD-MMM-YYYY')}
              </td>
            ))}
          </tr>
          <tr>
            <td className={headerCellStyle}>DETAILS</td>
            <td className={headerCellStyle}>TOTAL ({projectData?.currency?.toUpperCase()})</td>
            {periodLabels.map((label, i) => (
              <td key={i} className={headerCellStyle}>
                {label}
              </td>
            ))}
          </tr>
          <tr>
            <td
              colSpan={2 + periodLabels.length}
              className={twMerge(
                bodyCellStyle,
                'bg-gray-100 px-4 text-left font-bold text-gray-700'
              )}
            >
              PRO FORMA INCOME STATEMENT
            </td>
          </tr>
          {/* REVENUE SECTION */}
          <tr>
            <td className={getSubsectionCellStyle()}>
              OPERATING REVENUE
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(revenue?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={revenue?.total} />
            </td>
            {revenue?.array?.map((o, i) => (
              <td
                key={i}
                className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}
              >
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          {/* Revenue Individual Items - Direct under OPERATING REVENUE */}
          {/* OTC Individual Items */}
          {revenueData?.filter(item => item?.otc_amount > 0).map((item, idx) => {
            const otcItems = revenueData?.filter(item => item?.otc_amount > 0);
            const otcAmortisedItems = revenueData?.filter(item => item?.otc_amortise > 0);
            const mrcItems = revenueData?.filter(item => item?.mrc_amount > 0);
            
            const isLastOtcItem = idx === otcItems.length - 1;
            const hasOtcAmortised = otcAmortisedItems.length > 0;
            const hasMrc = mrcItems.length > 0;
            
            const isLastOverallItem = isLastOtcItem && !hasOtcAmortised && !hasMrc;
            
            return (
              <tr key={`otc-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastOverallItem ? '└──' : '├──'}</span>
                    {item?.description || 'Revenue Item'} ({item?.type || 'N/A'}) [OTC]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={item?.otc_amount || 0} />
                </td>
                {item?.revenue_otc_summary_by_year_array?.map((yearAmount, yearIdx) => (
                  <td key={yearIdx} className={getSubitemValueCellStyle()}>
                    <FormattedCell value={yearAmount} />
                  </td>
                ))}
              </tr>
            );
          })}
          
          {/* OTC Amortised Individual Items */}
          {revenueData?.filter(item => item?.otc_amortise > 0).map((item, idx) => {
            const otcAmortisedItems = revenueData?.filter(item => item?.otc_amortise > 0);
            const mrcItems = revenueData?.filter(item => item?.mrc_amount > 0);
            
            const isLastOtcAmortisedItem = idx === otcAmortisedItems.length - 1;
            const hasMrc = mrcItems.length > 0;
            
            const isLastOverallItem = isLastOtcAmortisedItem && !hasMrc;
            
            return (
              <tr key={`otc-amortised-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastOverallItem ? '└──' : '├──'}</span>
                    {item?.description || 'Revenue Item'} ({item?.type || 'N/A'}) [OTC Amortised]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={item?.otc_amortise || 0} />
                </td>
                {item?.revenue_otc_amortised_summary_by_year_array?.map((yearAmount, yearIdx) => (
                  <td key={yearIdx} className={getSubitemValueCellStyle()}>
                    <FormattedCell value={yearAmount} />
                  </td>
                ))}
              </tr>
            );
          })}
          
          {/* MRC Individual Items */}
          {revenueData?.filter(item => item?.mrc_amount > 0).map((item, idx) => {
            const mrcItems = revenueData?.filter(item => item?.mrc_amount > 0);
            const isLastMrcItem = idx === mrcItems.length - 1;
            
            return (
              <tr key={`mrc-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastMrcItem ? '└──' : '├──'}</span>
                    {item?.description || 'Revenue Item'} ({item?.type || 'N/A'}) [ARC]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={
                    (item?.mrc_amount || 0) * (parseInt(scenarioData?.contract_period_in_month, 10) || 0)
                  } />
                </td>
                {item?.revenue_mrc_summary_by_year_array?.map((yearAmount, yearIdx) => (
                  <td key={yearIdx} className={getSubitemValueCellStyle()}>
                    <FormattedCell value={yearAmount} />
                  </td>
                ))}
              </tr>
            );
          })}
          {/* COGS SECTION */}
          <tr>
            <td className={getSubsectionCellStyle()}>
              COGS
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(cogs?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={cogs?.total} />
            </td>
            {cogs?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}>
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          {/* COGS Individual Items - Always visible with smart connectors */}
          {cogs?.items?.map((item, idx) => (
            <tr key={idx}>
              <td className={getSubitemCellStyle()}>
                <div className="flex items-center gap-2">
                  <span className="text-blue-400">{idx === cogs.items.length - 1 ? '└──' : '├──'}</span>
                  {item?.details || 'COGS Item'}
                </div>
              </td>
              <td className={getSubitemValueCellStyle()}>
                <FormattedCell value={item?.opex_summary_by_year_array?.reduce((curr, prev) => curr + prev, 0) || 0} />
              </td>
              {item?.opex_summary_by_year_array?.map((yearAmount, yearIdx) => (
                <td key={yearIdx} className={getSubitemValueCellStyle()}>
                  <FormattedCell value={yearAmount} />
                </td>
              ))}
            </tr>
          ))}

          {/* Total COGS */}
          <tr className="bg-gray-100">
            <td className={twMerge(bodyCellStyle, 'px-4 text-left pl-4 font-bold')}>
              Total COGS
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold', getTextNumberColor(cogs?.total))}>
              <FormattedCell value={cogs?.total} />
            </td>
            {cogs?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, 'font-bold', getTextNumberColor(o))}>
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>

          {/* OPEX SECTION */}
          <tr>
            <td className={getSubsectionCellStyle()}>
              OPERATING COST
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(totalOpex?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={totalOpex?.total} />
            </td>
            {totalOpex?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}>
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          {/* OPEX Subitems - Always visible with smart connectors */}
          {/* O&M Individual Items - Direct under OPERATING COST */}
          {capexData?.filter(item => item?.om_needed).map((item, idx) => {
            const isLastOmItem = idx === capexData?.filter(item => item?.om_needed).length - 1;
            const hasOpexItems = regularOpex?.items?.length > 0;
            const hasUspItems = revenueData?.filter(item => item?.usp_needed === true).length > 0;
            const hasStampingFees = projectData?.stamping_fees_needed;
            
            // Determine if this is the last item overall
            const isLastOverallItem = isLastOmItem && !hasOpexItems && !hasUspItems && !hasStampingFees;
            
            return (
              <tr key={`om-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastOverallItem ? '└──' : '├──'}</span>
                    {item?.details || 'CAPEX Item'} ({item?.provider || 'N/A'}) [O&M]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={item?.om_summary_by_year_array?.reduce((curr, prev) => curr + prev, 0) || 0} />
                </td>
                {item?.om_summary_by_year_array?.map((yearAmount, yearIdx) => (
                  <td key={yearIdx} className={getSubitemValueCellStyle()}>
                    <FormattedCell value={yearAmount} />
                  </td>
                ))}
              </tr>
            );
          })}

          {/* Regular OPEX Individual Items - Direct under OPERATING COST */}
          {regularOpex?.items?.map((item, idx) => {
            const isLastOpexItem = idx === regularOpex?.items?.length - 1;
            const hasUspItems = revenueData?.filter(item => item?.usp_needed === true).length > 0;
            const hasStampingFees = projectData?.stamping_fees_needed;
            
            // Determine if this is the last item overall
            const isLastOverallItem = isLastOpexItem && !hasUspItems && !hasStampingFees;
            
            return (
              <tr key={`opex-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastOverallItem ? '└──' : '├──'}</span>
                    {item?.details || 'OPEX Item'} ({item?.provider || 'N/A'}) [OPEX]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={item?.opex_summary_by_year_array?.reduce((curr, prev) => curr + prev, 0) || 0} />
                </td>
                {item?.opex_summary_by_year_array?.map((yearAmount, yearIdx) => (
                  <td key={yearIdx} className={getSubitemValueCellStyle()}>
                    <FormattedCell value={yearAmount} />
                  </td>
                ))}
              </tr>
            );
          })}

          {/* USP Individual Items - Direct under OPERATING COST */}
          {revenueData?.filter(item => item?.usp_needed === true).map((item, idx) => {
            // Calculate USP amount for this specific item
            const itemRevenue = {
              mrc: (item?.mrc_amount || 0) * (parseInt(scenarioData?.contract_period_in_month, 10) || 0),
              otc: item?.otc_amount || 0,
              otc_amortised: item?.otc_amortise || 0
            };
            const itemTotalRevenue = itemRevenue.mrc + itemRevenue.otc + itemRevenue.otc_amortised;
            const itemUspAmount = itemTotalRevenue * (projectData?.rates_locked_during_creation?.usp / 100 || 0);
            
            const uspItems = revenueData?.filter(item => item?.usp_needed === true);
            const isLastUspItem = idx === uspItems.length - 1;
            const hasStampingFees = projectData?.stamping_fees_needed;
            
            // Determine if this is the last item overall
            const isLastOverallItem = isLastUspItem && !hasStampingFees;
            
            return (
              <tr key={`usp-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastOverallItem ? '└──' : '├──'}</span>
                    {item?.description || 'Revenue Item'} ({item?.product || 'N/A'}) [USP]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={itemUspAmount} />
                </td>
                {/* Calculate USP per period for this item */}
                {item?.revenue_mrc_summary_by_year_array?.map((yearAmount, yearIdx) => {
                  const yearlyItemRevenue = (
                    (yearIdx === 0 ? (item?.otc_amount || 0) : 0) +
                    (item?.revenue_otc_amortised_summary_by_year_array?.[yearIdx] || 0) +
                    (item?.revenue_mrc_summary_by_year_array?.[yearIdx] || 0)
                  );
                  const yearlyUspAmount = yearlyItemRevenue * (projectData?.rates_locked_during_creation?.usp / 100 || 0);
                  const displayUspAmount = convertToDisplayFormat([yearlyUspAmount])[0] || 0;
                  
                  return (
                    <td key={yearIdx} className={getSubitemValueCellStyle()}>
                      <FormattedCell value={displayUspAmount} />
                    </td>
                  );
                })}
              </tr>
            );
          })}

          {/* Stamping Fees Section - Only show if stamping fees are needed */}
          {projectData?.stamping_fees_needed && (
            <tr>
              <td className={getSubitemCellStyle()}>
                <div className="flex items-center gap-2">
                  <span className="text-blue-400">└──</span>
                  Stamping Fees
                </div>
              </td>
              <td className={getSubitemValueCellStyle()}>
                <FormattedCell value={stampingFees?.total} />
              </td>
              {stampingFees?.array?.map((o, i) => (
                <td key={i} className={getSubitemValueCellStyle()}>
                  <FormattedCell value={o} />
                </td>
              ))}
            </tr>
          )}


          <tr>
            <td className={getSubsectionCellStyle()}>EBITDA</td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(ebitda?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={ebitda?.total} />
            </td>
            {ebitda?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}>
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          {/* DEPRECIATION SECTION */}
          <tr>
            <td className={getSubsectionCellStyle()}>
              DEPRECIATION
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(depreciation?.total + rou?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={depreciation?.total + rou?.total} />
            </td>
            {depreciation?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o + (rou?.array?.[i] || 0)), 'bg-gray-50 font-bold')}>
                <FormattedCell value={o + (rou?.array?.[i] || 0)} />
              </td>
            ))}
          </tr>
          {/* Depreciation Individual Items - Direct under DEPRECIATION */}
          {/* CAPEX Depreciation Individual Items */}
          {capexData?.map((item, idx) => {
            const isLastCapexItem = idx === capexData.length - 1;
            const hasRouItems = rou?.items?.length > 0;
            
            const isLastOverallItem = isLastCapexItem && !hasRouItems;
            
            return (
              <tr key={`capex-dep-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastOverallItem ? '└──' : '├──'}</span>
                    {item?.details || 'CAPEX Item'} ({item?.provider || 'N/A'}) [CAPEX Depreciation]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={item?.depreciation_summary_by_year_array?.reduce((curr, prev) => curr + prev, 0) || 0} />
                </td>
                {item?.depreciation_summary_by_year_array?.map((yearAmount, yearIdx) => (
                  <td key={yearIdx} className={getSubitemValueCellStyle()}>
                    <FormattedCell value={yearAmount} />
                  </td>
                ))}
              </tr>
            );
          })}

          {/* ROU Individual Items */}
          {rou?.items?.map((item, idx) => {
            const isLastRouItem = idx === rou?.items?.length - 1;
            
            return (
              <tr key={`rou-${idx}`}>
                <td className={getSubitemCellStyle()}>
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">{isLastRouItem ? '└──' : '├──'}</span>
                    {item?.details || 'ROU Item'} [ROU Depreciation]
                  </div>
                </td>
                <td className={getSubitemValueCellStyle()}>
                  <FormattedCell value={item?.opex_summary_by_year_array?.reduce((curr, prev) => curr + prev, 0) || 0} />
                </td>
                {item?.opex_summary_by_year_array?.map((yearAmount, yearIdx) => (
                  <td key={yearIdx} className={getSubitemValueCellStyle()}>
                    <FormattedCell value={yearAmount} />
                  </td>
                ))}
              </tr>
            );
          })}
          <tr>
            <td className={getSubsectionCellStyle()}>EBIT</td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(ebit?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={ebit?.total} />
            </td>
            {ebit?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}>
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          <tr>
            <td className={getSubsectionCellStyle()}>EBIT MARGIN</td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(ebitMargin?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={ebitMargin?.total} isPercentage={true} />
            </td>
            <td
              colSpan={revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(bodyCellStyle, getTextNumberColor(ebitMargin?.total))}
            />
          </tr>
          <tr>
            <td className={getSubsectionCellStyle()}>TAX</td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(tax?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={tax?.total} />
            </td>
            {tax?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}>
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left font-bold')}>PAT</td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(pat?.total),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(pat?.total)}
            </td>
            {pat?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td
              colSpan={2 + revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(
                bodyCellStyle,
                'bg-gray-100 px-4 text-left font-bold text-gray-700'
              )}
            >
              PROJECTED CASH FLOW
            </td>
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left')}>NET INCOME</td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(netIncome?.total),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(netIncome?.total)}
            </td>
            {netIncome?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left')}>
              REVENUE AMORTIZATION - ADD OTC REVENUE
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(addBackOtcAmortised?.total),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(addBackOtcAmortised?.total)}
            </td>
            {addBackOtcAmortised?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left')}>
              ADD BACK DEPRECIATION
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(addBackDepreciation?.total),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(addBackDepreciation?.total)}
            </td>
            {addBackDepreciation?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left')}>
              OUTPAYMENT AMORTIZATION - LESS OTC OUTPAYMENT
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(0),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(0)}
            </td>
            {netIncome?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(0), 'bg-gray-50')}>
                {checkAndReplaceNumberWithZero(0)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left')}>
              NETT WORKING CAPITAL
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(nettWorkingCapital?.total),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(nettWorkingCapital?.total)}
            </td>
            {nettWorkingCapital?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left')}>
              NETT CASHFLOW AFTER CONTRACT END
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(nettCashflowAfterContractEnd?.total),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(nettCashflowAfterContractEnd?.total)}
            </td>
            {nettCashflowAfterContractEnd?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 px-4 text-left font-bold')}>
              CASH FLOW FROM OPERATION
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(cashFlowFromOperation?.total),
                'bg-gray-50 font-bold'
              )}
            >
              {checkAndReplaceNumberWithZero(cashFlowFromOperation?.total)}
            </td>
            {cashFlowFromOperation?.array?.map((o, i) => (
              <td
                key={i}
                className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}
              >
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          {/* CAPEX SECTION */}
          <tr>
            <td className={getSubsectionCellStyle()}>
              CAPEX
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(capex?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={capex?.total} />
            </td>
            {capex?.array?.map((o, i) => (
              <td
                key={i}
                className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}
              >
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          {/* CAPEX Individual Items - Always visible with smart connectors */}
          {capexData?.map((item, idx) => (
            <tr key={idx}>
              <td className={getSubitemCellStyle()}>
                <div className="flex items-center gap-2">
                  <span className="text-blue-400">{idx === capexData.length - 1 ? '└──' : '├──'}</span>
                  {item?.details || 'CAPEX Item'} ({item?.provider || 'N/A'})
                </div>
              </td>
              <td className={getSubitemValueCellStyle()}>
                <FormattedCell value={item?.amount || 0} />
              </td>
              {/* Display CAPEX amount in first year, 0 in other years */}
              {capex?.array?.map((_, yearIdx) => (
                <td key={yearIdx} className={getSubitemValueCellStyle()}>
                  <FormattedCell value={yearIdx === 0 ? item?.amount || 0 : 0} />
                </td>
              ))}
            </tr>
          ))}
          <tr>
            <td className={getSubsectionCellStyle()}>
              FREE CASHFLOW
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(freeCashFlow?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={freeCashFlow?.total} />
            </td>
            {freeCashFlow?.array?.map((o, i) => (
              <td
                key={i}
                className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}
              >
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          <tr>
            <td className={getSubsectionCellStyle()}>
              OPENING CASH
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(openingCash?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={openingCash?.total} />
            </td>
            {openingCash?.array?.map((o, i) => (
              <td
                key={i}
                className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}
              >
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          <tr>
            <td className={getSubsectionCellStyle()}>
              CLOSING CASH
            </td>
            <td
              className={twMerge(
                bodyCellStyle,
                getTextNumberColor(closingCash?.total),
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={closingCash?.total} />
            </td>
            {closingCash?.array?.map((o, i) => (
              <td
                key={i}
                className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-gray-50 font-bold')}
              >
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          <tr>
            <td
              colSpan={2 + revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(
                bodyCellStyle,
                'bg-gray-100 px-4 text-left font-bold text-gray-700'
              )}
            >
              RETURN
            </td>
          </tr>
          <tr className="bg-gray-50">
            <td className={getSubitemCellStyle()}>NPV</td>
            <td
              className={twMerge(
                bodyCellStyle,
                !goodXnpv && 'text-red-600',
                'bg-gray-50 font-bold'
              )}
            >
              <FormattedCell value={xnpv?.total} currency={projectData?.currency} showCurrency />
            </td>
            {xnpv?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), '')}>
                <FormattedCell value={o} />
              </td>
            ))}
          </tr>
          <tr className="bg-gray-50">
            <td className={getSubitemCellStyle()}>IRR</td>
            <td
              className={twMerge(
                bodyCellStyle,
                'bg-gray-50 font-bold',
                !goodIrr && 'text-red-600'
              )}
            >
              <FormattedCell value={irr?.total} isPercentage />
            </td>
            <td
              colSpan={revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(bodyCellStyle)}
            />
          </tr>
          <tr className="bg-gray-50">
            <td className={getSubitemCellStyle()}>PAYBACK PERIOD (YEARS)</td>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 font-bold')}>
              <FormattedCell value={paybackPeriodInYears?.total} />
            </td>
            <td
              colSpan={revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(bodyCellStyle)}
            />
          </tr>
          <tr className="bg-gray-50">
            <td className={getSubitemCellStyle()}>DPP (YEARS)</td>
            <td className={twMerge(bodyCellStyle, 'bg-gray-50 font-bold')}>
              <FormattedCell value={dppInYears?.total} />
            </td>
            <td
              colSpan={revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(bodyCellStyle)}
            />
          </tr>
          <tr>
            <td
              colSpan={2 + revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(
                bodyCellStyle,
                'bg-gray-100 px-4 text-left font-bold text-gray-700'
              )}
            >
              WORKING CAPITAL
            </td>
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'border-0 font-bold')} />
            <td className={twMerge(bodyCellStyle, 'border-0 font-bold')}>(DAYS)</td>
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'border-0 px-4 text-left font-bold')}>
              ACCOUNT RECEIVABLES
            </td>
            <td className={twMerge(bodyCellStyle, 'border-0 font-bold')}>
              {accountReceivableDays}
            </td>
            <td
              colSpan={revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(bodyCellStyle, 'border-0 font-bold')}
            />
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100 px-4 text-left')}>
              Opening Balance
            </td>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100')} />
            {accountReceivableOpeningBalance?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-pink-100')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100 px-4 text-left')}>Movement</td>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100')} />
            {accountReceivableMovement?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-pink-100')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-400 px-4 text-left')}>
              Closing Balance
            </td>
            <td className={twMerge(bodyCellStyle, 'bg-gray-400')} />
            {accountReceivableClosingBalance?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), ' bg-gray-400')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'border-0 px-4 text-left font-bold')}>
              ACCOUNT PAYABLES (EXC INTERNAL COST)
            </td>
            <td className={twMerge(bodyCellStyle, 'border-0 font-bold')}>{accountPayableDays}</td>
            <td
              colSpan={revenueData?.[0]?.revenue_mrc_summary_by_year_array?.length}
              className={twMerge(bodyCellStyle, 'border-0 font-bold')}
            />
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100 px-4 text-left')}>
              Opening Balance
            </td>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100')} />
            {accountPayableOpeningBalance?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-pink-100')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100 px-4 text-left')}>Movement</td>
            <td className={twMerge(bodyCellStyle, 'bg-pink-100')} />
            {accountPayableMovement?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), 'bg-pink-100')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
          <tr>
            <td className={twMerge(bodyCellStyle, 'bg-gray-400 px-4 text-left')}>
              Closing Balance
            </td>
            <td className={twMerge(bodyCellStyle, 'bg-gray-400')} />
            {accountPayableClosingBalance?.array?.map((o, i) => (
              <td key={i} className={twMerge(bodyCellStyle, getTextNumberColor(o), ' bg-gray-400')}>
                {checkAndReplaceNumberWithZero(o)}
              </td>
            ))}
          </tr>
        </tbody>
      </table>
    </ResponsiveTableWrapper>
  );

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    setIsComponentLoading(true);
    setError(null);
    
    const errors = [];
    
    try {
      const response = await axios.get(`${FA_ENDPOINT}/scenario/id/${scenarioId}`);
      setScenarioData(response?.data?.data?.[0] || {});
    } catch (err) {
      setScenarioData({});
      errors.push({ type: 'scenario', error: err });
    }
    try {
      const response = await axios.get(`${FA_ENDPOINT}/capex/scenario_id/${scenarioId}`);
      setCapexData(response?.data?.data || []);
    } catch (err) {
      setCapexData([]);
      errors.push({ type: 'capex', error: err });
    }
    try {
      const response = await axios.get(`${FA_ENDPOINT}/opex/scenario_id/${scenarioId}`);
      setOpexData(response?.data?.data || []);
    } catch (err) {
      setOpexData([]);
      errors.push({ type: 'opex', error: err });
    }
    try {
      const response = await axios.get(`${FA_ENDPOINT}/revenue/scenario_id/${scenarioId}`);
      setRevenueData(response?.data?.data || []);
    } catch (err) {
      setRevenueData([]);
      errors.push({ type: 'revenue', error: err });
    }
    try {
      const response = await axios.get(`${FA_ENDPOINT}/approval/project_id/${projectId}`);

      if (response?.data?.data) {
        setApprovalsData(response?.data?.data);
        setEndorsementSwitchIsDisabled(
          !(
            response?.data?.data?.find((o) => o?.status?.toLowerCase() === 'pending')?.type ===
            'approver'
          )
        );
      }
    } catch (err) {
      setEndorsementSwitchIsDisabled(false);
      errors.push({ type: 'approval', error: err });
    }
    
    // Set error state if any errors occurred
    if (errors.length > 0) {
      const errorTypes = errors.map(e => e.type).join(', ');
      const isNetworkError = errors.some(e => !e.error?.response);
      
      setError({
        type: isNetworkError ? 'network' : 'data',
        message: `Failed to load: ${errorTypes}`,
        details: errors.map(e => `${e.type}: ${e.error?.message || 'Unknown error'}`).join('; '),
        retry: () => {
          setRetryCount(prev => prev + 1);
          fetchData();
        }
      });
    }
    
    dispatch(setIsLoading(false));
    setIsComponentLoading(false);
  };

  useEffect(() => {
    if (projectId && scenarioId) fetchData();
  }, [projectId, scenarioId]);

  useEffect(() => {
    if (isSummary) return;
    dispatch(
      setBreadCrumbsList([
        {
          linkTo: '/fa',
          label: 'FA List',
        },
        {
          linkTo: `/fa/project/${projectId}`,
          label: projectData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=capex`,
          label: scenarioData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=fa`,
          label: 'FA',
        },
      ])
    );
  }, [projectId, projectData?.name, scenarioData?.name, scenarioId]);

  // Handle individual cell rendering for combined table
  if (cellType) {
    switch (cellType) {
      case 'approver':
        return (
          <div className="flex flex-col items-center justify-center">
            <Switch
              onChange={async (event) => {
                setScenarioData({ ...scenarioData, scenario_endorsed: event.target.checked });
                try {
                  const response = await axios.put(
                    `${FA_ENDPOINT}/scenario/${scenarioData?.id}`,
                    {
                      ...scenarioData,
                      scenario_endorsed: event.target.checked,
                    }
                  );
                  let statusVariant;
                  let message;
                  if (response.data.status === 'success') {
                    statusVariant = 'success';
                    message = event.target.checked ? 'Scenario endorsed' : 'Scenario rejected';
                  } else {
                    statusVariant = 'error';
                    message = 'Failed';
                  }
                  enqueueSnackbar(message, {
                    variant: statusVariant,
                  });
                } catch {
                  enqueueSnackbar('Failed', {
                    variant: 'error',
                  });
                }
              }}
              checked={scenarioData?.scenario_endorsed}
              disabled={endorsementSwitchIsDisabled || !goodXnpv || !goodIrr}
            />
            <span className="text-xs mt-1 opacity-75">
              {scenarioData?.scenario_endorsed ? 'Endorsed' : 'Pending'}
            </span>
          </div>
        );
      case 'currency':
        return <span className="font-medium text-gray-900">{projectData?.currency?.toUpperCase()}</span>;
      case 'revenue':
        return <FormattedCell value={revenue?.total} />;
      case 'otc':
        return <FormattedCell value={revenueOtc?.total} />;
      case 'otc_amortised':
        return <FormattedCell value={revenueOtcAmortised?.total} />;
      case 'arc':
        return <FormattedCell value={revenueMrc?.total} />;
      case 'opex':
        return <FormattedCell value={totalOpex?.total} />;
      case 'om':
        return <FormattedCell value={om?.total} />;
      case 'outpayment':
        return <FormattedCell value={regularOpex?.total} />;
      case 'usp':
        return <FormattedCell value={usp?.total} />;
      case 'ebitda':
        return <FormattedCell value={ebitda?.total} />;
      case 'depreciation':
        return <FormattedCell value={depreciation?.total} />;
      case 'ebit':
        return <FormattedCell value={ebit?.total} />;
      case 'ebit_margin':
        return <FormattedCell value={ebitMargin?.total} isPercentage={true} />;
      case 'tax':
        return <FormattedCell value={tax?.total} />;
      case 'pat':
        return <FormattedCell value={pat?.total} />;
      case 'pat_margin':
        return <FormattedCell value={patMargin?.total} isPercentage={true} />;
      case 'cogs':
        return <FormattedCell value={cogs?.total} />;
      case 'stamping_fees':
        return <FormattedCell value={stampingFees?.total} />;
      case 'rou':
        return <FormattedCell value={rou?.total} />;
      case 'net_income':
        return <FormattedCell value={netIncome?.total} />;
      case 'add_back_depreciation':
        return <FormattedCell value={addBackDepreciation?.total} />;
      case 'add_back_otc_amortised':
        return <FormattedCell value={addBackOtcAmortised?.total} />;
      case 'nett_working_capital':
        return <FormattedCell value={nettWorkingCapital?.total} />;
      case 'nett_cashflow_after_contract_end':
        return <FormattedCell value={nettCashflowAfterContractEnd?.total} />;
      case 'cash_flow_from_operation':
        return <FormattedCell value={cashFlowFromOperation?.total} />;
      case 'capex':
        return <FormattedCell value={capex?.total} />;
      case 'free_cash_flow':
        return <FormattedCell value={freeCashFlow?.total} />;
      case 'opening_cash':
        return <FormattedCell value={openingCash?.total} />;
      case 'closing_cash':
        return <FormattedCell value={closingCash?.total} />;
      case 'xnpv':
        return <FormattedCell value={xnpv?.total} currency={projectData?.currency} showCurrency={true} />;
      case 'irr':
        return <FormattedCell value={irr?.total} isPercentage={true} />;
      case 'payback_period':
        return <FormattedCell value={paybackPeriodInYears?.total} className="font-medium" />;
      case 'dpp':
        return <FormattedCell value={dppInYears?.total} className="font-medium" />;
      default:
        return <span>-</span>;
    }
  }

  // Show error state for summary
  if (isSummary && error) {
    return (
      <div className="p-2">
        {error.type === 'network' ? (
          <NetworkError onRetry={error.retry} className="text-xs" />
        ) : (
          <DataError onRetry={error.retry} details={error.details} className="text-xs" />
        )}
      </div>
    );
  }

  // Show loading state for summary
  if (isSummary && isLoading) {
    return <LoadingSpinner size="sm" />;
  }

  if (isSummary)
    return (
      <table className="bg-white text-black border-collapse table-fixed">
        <tbody>
          {/* Contract Terms */}
          <tr>
            <td className={getEnhancedCellStyle('contract')}>
              {scenarioData?.contract_period_in_month} months
            </td>
          </tr>
          
          {/* Approver Endorsement */}
          {showApproverEndorsementCell && (
            <tr>
              <td className={getEnhancedCellStyle('approver')}>
                <div className="flex flex-col items-center justify-center">
                  <Switch
                    onChange={async (event) => {
                      setScenarioData({ ...scenarioData, scenario_endorsed: event.target.checked });
                      try {
                        const response = await axios.put(
                          `${FA_ENDPOINT}/scenario/${scenarioData?.id}`,
                          {
                            ...scenarioData,
                            scenario_endorsed: event.target.checked,
                          }
                        );
                        let statusVariant;
                        let message;
                        if (response.data.status === 'success') {
                          statusVariant = 'success';
                          message = event.target.checked ? 'Scenario endorsed' : 'Scenario rejected';
                        } else {
                          statusVariant = 'error';
                          message = 'Failed';
                        }
                        enqueueSnackbar(message, {
                          variant: statusVariant,
                        });
                      } catch {
                        enqueueSnackbar('Failed', {
                          variant: 'error',
                        });
                      }
                    }}
                    checked={scenarioData?.scenario_endorsed}
                    disabled={endorsementSwitchIsDisabled || !goodXnpv || !goodIrr}
                  />
                  <span className="text-xs mt-1 opacity-75">
                    {scenarioData?.scenario_endorsed ? 'Endorsed' : 'Pending'}
                  </span>
                </div>
              </td>
            </tr>
          )}
          
          {/* FA ID */}
          <tr>
            <td className={getEnhancedCellStyle('header')}>
              {scenarioData?.scenario_id}
            </td>
          </tr>
          
          {/* Scenario Name */}
          <tr>
            <td className={getEnhancedCellStyle('header')}>
              {scenarioData?.name}
            </td>
          </tr>
          
          {/* Currency */}
          <tr>
            <td className={getEnhancedCellStyle('subsection')}>
              {projectData?.currency?.toUpperCase()}
            </td>
          </tr>
          
          {/* Profit and Loss Section Header */}
          <tr>
            <td className="bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold text-center py-3 border-0">
              <div className="flex items-center justify-center gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span className="text-sm tracking-wide">PROFIT AND LOSS</span>
              </div>
            </td>
          </tr>
          {/* REVENUE SECTION */}
          <tr>
            <td
              className={twMerge(
                getEnhancedCellStyle('subsection'),
                'cursor-pointer',
                getTextNumberColor(revenue?.total)
              )}
              onClick={() => setRevenueExpanded(!revenueExpanded)}
            >
              <div className="flex items-center justify-between">
                <span>{checkAndReplaceNumberWithZero(revenue?.total)}</span>
                <svg
                  className={`h-4 w-4 transition-transform duration-200 ${revenueExpanded ? 'rotate-180 transform' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </td>
          </tr>
          {revenueExpanded && (
            <>
              <tr>
                <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(revenueOtc?.total))}>
                  {checkAndReplaceNumberWithZero(revenueOtc?.total)}
                </td>
              </tr>
              <tr>
                <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(revenueOtcAmortised?.total))}>
                  {checkAndReplaceNumberWithZero(revenueOtcAmortised?.total)}
                </td>
              </tr>
              <tr>
                <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(revenueMrc?.total))}>
                  {checkAndReplaceNumberWithZero(revenueMrc?.total)}
                </td>
              </tr>
            </>
          )}
          
          {/* OTC */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(revenueOtc?.total))}>
              {checkAndReplaceNumberWithZero(revenueOtc?.total)}
            </td>
          </tr>
          
          {/* OTC Amortised */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(revenueOtcAmortised?.total))}>
              {checkAndReplaceNumberWithZero(revenueOtcAmortised?.total)}
            </td>
          </tr>
          
          {/* ARC */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(revenueMrc?.total))}>
              {checkAndReplaceNumberWithZero(revenueMrc?.total)}
            </td>
          </tr>
          
          {/* OPEX Section */}
          <tr>
            <td className={getEnhancedCellStyle('subsection')}>
              {checkAndReplaceNumberWithZero(totalOpex?.total)}
            </td>
          </tr>
          
          {/* O&M */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(om?.total))}>
              {checkAndReplaceNumberWithZero(om?.total)}
            </td>
          </tr>
          
          {/* Outpayment */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(regularOpex?.total))}>
              {checkAndReplaceNumberWithZero(regularOpex?.total)}
            </td>
          </tr>
          
          {/* USP */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle(), getTextNumberColor(usp?.total))}>
              {checkAndReplaceNumberWithZero(usp?.total)}
            </td>
          </tr>
          
          {/* EBITDA */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('highlight'), getTextNumberColor(ebitda?.total))}>
              {checkAndReplaceNumberWithZero(ebitda?.total)}
            </td>
          </tr>
          
          {/* DEPRECIATION */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('subsection'), getTextNumberColor(depreciation?.total))}>
              {checkAndReplaceNumberWithZero(depreciation?.total)}
            </td>
          </tr>
          
          {/* EBIT */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('highlight'), getTextNumberColor(ebit?.total))}>
              {checkAndReplaceNumberWithZero(ebit?.total)}
            </td>
          </tr>
          
          {/* EBIT Margin */}
          <tr>
            <td className={getEnhancedCellStyle('highlight')}>
              {checkAndReplaceNumberWithZero(ebitMargin?.total)} %
            </td>
          </tr>
          
          {/* TAX */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('subsection'), getTextNumberColor(tax?.total))}>
              {checkAndReplaceNumberWithZero(tax?.total)}
            </td>
          </tr>
          
          {/* PAT */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('highlight'), getTextNumberColor(pat?.total))}>
              {checkAndReplaceNumberWithZero(pat?.total)}
            </td>
          </tr>
          
          {/* PAT MARGIN */}
          <tr>
            <td className={getEnhancedCellStyle('highlight')}>
              {checkAndReplaceNumberWithZero(patMargin?.total * 100)} %
            </td>
          </tr>
          
          {/* RETURNS Section */}
          <tr>
            <td className="bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold text-center py-3 border-0">
              <div className="flex items-center justify-center gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <span className="text-sm tracking-wide">RETURNS</span>
              </div>
            </td>
          </tr>
          
          {/* NPV */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('subsection'), getTextNumberColor(goodXnpv ? 0 : -1))}>
              {projectData?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(xnpv?.total)}
            </td>
          </tr>
          
          {/* IRR */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('subsection'), getTextNumberColor(goodIrr ? 0 : -1))}>
              {humanizeNumber(irr?.total)}
              {humanizeNumber(irr?.total) !== 'N/A' ? ' %' : ''}
            </td>
          </tr>
          
          {/* Payback Period */}
          <tr>
            <td className={getEnhancedCellStyle('subsection')}>
              {checkAndReplaceNumberWithZero(paybackPeriodInYears?.total)}
            </td>
          </tr>
          
          {/* DPP */}
          <tr>
            <td className={getEnhancedCellStyle('subsection')}>
              {checkAndReplaceNumberWithZero(dppInYears?.total)}
            </td>
          </tr>
          
          {/* CAPEX */}
          <tr>
            <td className={twMerge(getEnhancedCellStyle('section'), getTextNumberColor(calculateData(capexData, 'capex_total_sum_array')?.total))}>
              {checkAndReplaceNumberWithZero(calculateData(capexData, 'capex_total_sum_array')?.total)}
            </td>
          </tr>
        </tbody>
      </table>
    );

  // Show error state for detailed view
  if (error && !isSummary) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex h-full flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          {error.type === 'network' ? (
            <NetworkError onRetry={error.retry} />
          ) : (
            <DataError onRetry={error.retry} details={error.details} />
          )}
        </div>
      </div>
    );
  }

  // Show loading state for detailed view
  if (isLoading && !isSummary) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex h-full flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <FinancialTableSkeleton rows={15} columns={8} />
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      {showExportButton && !exportMode && (
        <div className="flex justify-end mb-4">
          <button
            type="button"
            className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
            onClick={() => {
              setParam({ screenshotDialogOpen: true });
            }}
          >
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            Export Details
          </button>
        </div>
      )}
      <div className="flex h-full flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        {faDetailsPart}
      </div>
      <ScreenshotDialog fileName={`FA-${projectData?.name}-${scenarioData?.name}.pdf`} isLandscape>
        <div className="flex w-full flex-col gap-4 p-4">
          <div className="bg-primary w-full text-center text-white">FINANCIAL ANALYSIS DETAILS</div>

          <div className="mx-auto w-2/3 overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <tbody>
                <tr>
                  <td className="text-xs">Customer</td>
                  <td className="text-xs">{projectData?.customer_name}</td>
                </tr>
                <tr>
                  <td className="text-xs">Contract Terms</td>
                  <td className="text-xs">{scenarioData?.contract_period_in_month} Months</td>
                  <td className="text-xs">RFS Date</td>
                  <td className="text-xs">{date}</td>
                </tr>
                <tr>
                  <td className="text-xs">FA ID</td>
                  <td className="text-xs">{scenarioData?.scenario_id}</td>
                  <td />
                </tr>
                <tr />
                <tr>
                  <td className="text-xs font-bold">Final Status</td>
                  <td />
                </tr>
                <tr>
                  <td className="text-xs font-bold">Role</td>
                  <td className="text-xs font-bold">Name</td>
                  <td className="text-xs font-bold">Status</td>
                  <td className="text-xs font-bold">Timestamp</td>
                </tr>
                {approvalsData?.map((o, i) => (
                  <tr key={i}>
                    <td className="text-xs">{toUpperCaseFirstLetter(o?.type)}</td>
                    <td className="text-xs">{o?.user_name_array?.[0]}</td>
                    <td className="text-xs uppercase">{o?.status}</td>
                    <td className="text-xs">{o?.updated_at}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {faDetailsPart}
        </div>
      </ScreenshotDialog>
    </div>
  );
};

FinancialAnalysis.propTypes = {
  isSummary: PropTypes.bool,
  showApproverEndorsementCell: PropTypes.bool,
  scenarioId: PropTypes.string,
  showExportButton: PropTypes.bool,
  exportMode: PropTypes.bool,
};

export default FinancialAnalysis;
