import React, { useState } from 'react';
import PropTypes from 'prop-types';
import * as yup from 'yup';
import moment from 'moment';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Components
import { TextInput, SelectInput, DateInput } from '../../Shared/CustomInput';

const OpexFormDialog = ({
  open,
  onClose,
  editMode,
  dialogData,
  scenarioId,
  projectId,
  onSubmit,
  projectData,
  scenarioData
}) => {
  const [validationErrors, setValidationErrors] = useState({});

  // Function to calculate end date based on start date and contract period
  const calculateEndDate = (startDate) => {
    if (!startDate || !scenarioData?.contract_period_in_month) return '';

    try {
      const start = moment(startDate);
      if (!start.isValid()) return '';

      const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 1;
      const end = start
        .clone()
        .add(contractMonths, 'month')
        .subtract(1, 'days');

      return end.format('YYYY-MM-DD');
    } catch (error) {
      console.error('Error calculating end date:', error);
      return '';
    }
  };

  const schema = yup.object({
    start_date: yup.string().required('Start date is required'),
    end_date: yup.string().required('End date is required'),
    currency: yup.string().required('Please provide currency.'),
    details: yup.string().required('Please provide details.'),
    mrc_amount: yup.number().required('Please provide MRC amount.'),
    opex_summary_by_year_array: yup.array().of(yup.number()).default([]),
    otc_amount: yup.number().required('Please provide OTC amount.'),
    otc_amortise_amount: yup.number().required('Please provide OTC amortise amount.'),
    payment_term_in_days: yup.number().required('Please provide payment term.'),
    project_id: yup.string().required('Please provide project ID.').default(projectId),
    provider: yup.string().required('Please provide provider.'),
    provider_others: yup.string().when('provider', {
      is: (val) => val === 'Others',
      then: (schema) => schema.required('Please specify other provider.'),
      otherwise: (schema) => schema.nullable()
    }),
    scenario_id: yup.string().required('Please provide scenario ID.').default(scenarioId),
    leg_a: yup.string().required('Please provide leg A.'),
    leg_b: yup.string().required('Please provide leg B.'),
  });

  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    let updatedData = {
      ...dialogData,
      [name]: value,
    };

    if (name === 'provider' && value !== 'Others') {
      updatedData = {
        ...updatedData,
        provider_others: '', // Clear provider_others if provider is not 'Others'
      };
    }

    onSubmit('update', updatedData);
  };

  // Ensure data has all required numeric fields properly formatted
  const sanitizeOpexData = (data) => {
    const result = { ...data };

    // Ensure all numeric fields are properly converted to numbers
    result.otc_amount = Number(data?.otc_amount || 0);
    result.otc_amortise_amount = Number(data?.otc_amortise_amount || 0);
    result.mrc_amount = Number(data?.mrc_amount || 0);

    // For backwards compatibility, if otc_amortise_amount is 0 but otc_amount is set,
    // use the same value for amortization (common in many OPEX entries)
    if (result.otc_amortise_amount === 0 && result.otc_amount > 0) {
      result.otc_amortise_amount = result.otc_amount;
      console.log('Dialog: Auto-set otc_amortise_amount to match otc_amount:', result.otc_amount);
    }

    return result;
  };

  // Updated getMetrix function to match the one in OpexUtils.js
  const getMetrix = (data) => {
    // Sanitize the data to ensure all numeric fields are properly formatted
    const sanitizedData = sanitizeOpexData(data);

    // Debug the inputs to help identify issues
    const startYear = moment(scenarioData.project_start_date).year();
    const rfsYear = moment(scenarioData.expected_rfs_date).year();
    const yearDifference = Math.max(0, rfsYear - startYear);
    const contractMonths = Number(scenarioData?.contract_period_in_month) || 120;
    const contractYears = Math.ceil(contractMonths / 12);
    const totalYears = yearDifference + contractYears;

    console.log('Dialog getMetrix calculation:', {
      data_start_date: sanitizedData?.start_date,
      data_otc_amount: sanitizedData?.otc_amount,
      data_otc_amortise: sanitizedData?.otc_amortise_amount,
      data_mrc: sanitizedData?.mrc_amount,
      project_start_date: scenarioData?.project_start_date,
      expected_rfs_date: scenarioData?.expected_rfs_date,
      startYear,
      rfsYear,
      yearDifference,
      contractMonths,
      contractYears,
      totalYears
    });

    const calculateAmortiseArray = () => {
      // Use year calculations from parent scope
      const totalAmount = sanitizedData?.otc_amortise_amount;

      // Create array with zeros for gap years
      const result = Array(totalYears).fill(0);

      // Fill contract years with amortized amounts
      const amountPerYear = totalAmount / contractYears;
      // CAPEX-style: fill zeros for all years, then fill values only from yearDifference onward
      for (let i = 0; i < totalYears; i++) {
        result[i] = i < yearDifference ? 0 : amountPerYear;
      }

      console.log('Amortise array calculation:', {
        totalAmount,
        amountPerYear,
        result
      });

      // (Removed duplicate/unused code block)
      return result;
    };

    const calculateMrcArray = () => {
      // Use year calculations from parent scope
      const monthlyAmount = sanitizedData?.mrc_amount;

      // Create array with zeros for gap years
      const result = Array(totalYears).fill(0);

      // Fill contract years with MRC amounts
      const amountPerYear = monthlyAmount * 12;
      // CAPEX-style: fill zeros for all years, then fill values only from yearDifference onward
      for (let i = 0; i < totalYears; i++) {
        result[i] = i < yearDifference ? 0 : amountPerYear;
      }

      console.log('MRC array calculation:', {
        monthlyAmount,
        amountPerYear,
        result
      });

      // Removed unused date parsing logic from calculateMrcArray

      // (Removed duplicate/unused code block)
      return result;
    };

    // Calculate both arrays
    const result = {
      amortiseArray: calculateAmortiseArray(),
      mrcArray: calculateMrcArray(),
    };

    console.log('Dialog getMetrix result:', result);

    return result;
  };

  const handleDialogAction = async (action) => {
    if (action) {
      try {
        // Clear previous validation errors
        setValidationErrors({});

        console.log('Creating OPEX with data:', dialogData);

        // Add dates and convert numeric values
        let payload = {
          ...dialogData,
          start_date: scenarioData?.expected_rfs_date,
          end_date: calculateEndDate(scenarioData?.expected_rfs_date),
          mrc_amount: Number(dialogData?.mrc_amount || 0),
          otc_amount: Number(dialogData?.otc_amount || 0),
          otc_amortise_amount: Number(dialogData?.otc_amortise_amount || 0),
          payment_term_in_days: Number(dialogData?.payment_term_in_days || 30)
        };

        // Sanitize the payload data
        payload = sanitizeOpexData(payload);

        // If provider is 'Others', use provider_others
        if (payload.provider === 'Others') {
          payload.provider = payload.provider_others || '';
        }

        // Remove provider_others from payload
        const { provider_others, ...payloadForValidation } = payload;

        // Validate the payload
        console.log('Validating payload:', payloadForValidation);
        await schema.validate(payloadForValidation, { abortEarly: false });
        console.log('Validation successful');

        const temporaryHolder = {
          mrc_amount: payload.mrc_amount,
          otc_amount: payload.otc_amount,
          otc_amortise_amount: payload.otc_amortise_amount,
        };

        // Standardize with project currency
        if (projectData?.currency !== payload?.currency) {
          if (projectData?.exchange_rate === 0) {
            return { success: false, message: "Exchange rate can't be 0" };
          }
          if (projectData?.currency === 'myr') {
            payload.mrc_amount *= projectData?.exchange_rate;
            payload.otc_amount *= projectData?.exchange_rate;
            payload.otc_amortise_amount *= projectData?.exchange_rate;
          } else {
            payload.mrc_amount /= projectData?.exchange_rate;
            payload.otc_amount /= projectData?.exchange_rate;
            payload.otc_amortise_amount /= projectData?.exchange_rate;
          }
        }

        // Ensure correct types and required fields
        payload.payment_term_in_days = Number(payload.payment_term_in_days);
        payload.project_id = projectId;
        payload.scenario_id = scenarioId;
        if ('opex_summary_by_year_array' in payload) {
          delete payload.opex_summary_by_year_array;
        }
        // --- END FIX ---

        const metrix = getMetrix(payload);

        // Calculate total years needed for summary
        // Parse start date correctly
        const startDate = payload.start_date || '';
        let startMonth = 0;

        try {
          // Try to directly extract the month value from the date format
          if (startDate.includes('/')) {
            // For formats like DD/MM/YYYY or MM/DD/YYYY
            const parts = startDate.split('/');
            if (parts.length === 3) {
              if (parts[1].length <= 2 && parseInt(parts[1]) <= 12) {
                // Assuming it's DD/MM/YYYY format for "1/12/2024"
                startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
              }
            }
          } else if (startDate.includes('-')) {
            // For formats like YYYY-MM-DD
            const parts = startDate.split('-');
            if (parts.length === 3 && parts[0].length === 4) {
              // Standard ISO format YYYY-MM-DD
              startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
            }
          }

          // Fallback: try to use moment if direct parsing fails
          if (isNaN(startMonth)) {
            const m = moment(startDate, ["DD/MM/YYYY", "YYYY-MM-DD"]);
            startMonth = m.isValid() ? m.month() : 0;
          }
        } catch (error) {
          console.error("Date parsing error in dialog action:", error);
          startMonth = 0; // Default to January
        }

        // Make sure we have a valid start month (0-11)
        startMonth = startMonth >= 0 && startMonth <= 11 ? startMonth : 0;

        // Debug: log scenario dates before gap calculation
        console.log('SCENARIO DATES', {
          project_start_date: scenarioData.project_start_date,
          expected_rfs_date: scenarioData.expected_rfs_date
        });
        // Calculate year difference for gap years
        const startYear = moment(scenarioData.project_start_date).year();
        const rfsYear = moment(scenarioData.expected_rfs_date).year();
        // Calculate gap as difference in years only
        const yearDifference = Math.max(0, rfsYear - startYear);

        // Get contract info
        const contractMonths = Number(scenarioData?.contract_period_in_month) || 120;
        const contractYears = Math.ceil(contractMonths / 12);

        // Total years = gap years + contract years
        const totalYears = yearDifference + contractYears;

        // Create summary array with gap years
        payload.opex_summary_by_year_array = Array(totalYears).fill(0);

        // Add OTC amount in first active year (after gap)
        // Only set OTC amount if yearDifference > 0, otherwise leave Y0 as 0
        if (yearDifference > 0) {
          payload.opex_summary_by_year_array[yearDifference] = payload.otc_amount;
        }

        // Add amortise and MRC amounts for active years
        // Ensure amortise and mrc arrays are zero before yearDifference
        // Force Y0 to zero for amortise and MRC if there is a gap
        for (let i = 0; i < yearDifference; i++) {
          payload.opex_summary_by_year_array[i] = 0;
        }
        for (let i = yearDifference; i < totalYears; i++) {
          payload.opex_summary_by_year_array[i] +=
            (metrix.amortiseArray[i - yearDifference] || 0) +
            (metrix.mrcArray[i - yearDifference] || 0);
        }

        console.log('Final OPEX summary array:', {
          yearDifference,
          contractYears,
          totalYears,
          projectStartDate: scenarioData.project_start_date,
          rfsDate: scenarioData.expected_rfs_date,
          otcAmount: payload.otc_amount,
          summary: payload.opex_summary_by_year_array,
          metrixAmortise: metrix.amortiseArray,
          metrixMrc: metrix.mrcArray,
          summaryTable: payload.opex_summary_by_year_array.map((v, i) => ({ year: i, value: v }))
        });

        payload = { ...payload, ...temporaryHolder };

        let finalPayload = { ...payloadForValidation };
        if (dialogData.provider === 'Others') {
          finalPayload.provider = dialogData.provider_others || '';
        } else {
          finalPayload.provider = dialogData.provider || '';
        }
        // Ensure provider_others is not part of the final payload unless specifically handled by backend
        delete finalPayload.provider_others;

        // Use the complete payload with all fields
        const completePayload = {
          ...payload,
          provider: finalPayload.provider
        };

        console.log('Submitting final payload:', completePayload);
        onSubmit(action, completePayload);
      } catch (error) {
        console.error("Form validation error:", error);

        // Extra error logging for debugging
        if (typeof window !== "undefined") {
          window.__lastOpexError = error;
        }
        if (error && error.stack) {
          console.error("Stack trace:", error.stack);
        }
        console.error("Payload at error:", payload);
        console.error("Metrix at error:", metrix);

        // Format validation errors
        if (error?.inner) {
          const errors = {};
          error.inner.forEach((err) => {
            errors[err.path] = err.message;
            console.log(`Validation error for ${err.path}:`, err.message);
          });
          setValidationErrors(errors);
        }

        // Show specific error message
        const errorMessage = error?.inner?.length > 0
          ? error.inner.map(err => err.message).join(', ')
          : error.message || 'Validation failed';

        return { success: false, message: errorMessage };
      }
    } else {
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={() => onClose()}>
      <DialogTitle className="bg-fa text-center text-white">
        {!editMode ? 'Add' : 'Edit'} OPEX
      </DialogTitle>
      <DialogContent>
        <div className="flex">
          <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <DateInput
              name="start_date"
              value={scenarioData?.expected_rfs_date || ''}
              placeholder="Start Date"
              disabled={true}
              returnedFormat="YYYY-MM-DD"
            />
            <DateInput
              name="end_date"
              value={calculateEndDate(scenarioData?.expected_rfs_date)}
              placeholder="End Date"
              disabled={true}
              returnedFormat="YYYY-MM-DD"
            />
            <SelectInput
              name="provider"
              value={dialogData?.provider || ''}
              placeholder="Provider"
              onChange={handleDialogDataChange}
              error={validationErrors?.provider}
              options={[
                { value: 'GNT', label: 'GNT' },
                { value: '3rd party', label: '3rd Party' },
                { value: 'ROU', label: 'ROU' },
                { value: 'COGS', label: 'COGS' },
                { value: 'Others', label: 'Others' },
              ]}
            />
            {dialogData?.provider === 'Others' && (
              <TextInput
                name="provider_others"
                value={dialogData?.provider_others || ''}
                placeholder="Specify Other Provider"
                onChange={handleDialogDataChange}
                error={validationErrors?.provider_others}
              />
            )}
            <TextInput
              name="details"
              value={dialogData?.details}
              placeholder="Details"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="leg_a"
              value={dialogData?.leg_a}
              placeholder="Leg A"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="leg_b"
              value={dialogData?.leg_b}
              placeholder="Leg B"
              onChange={handleDialogDataChange}
            />
            <SelectInput
              type="number"
              name="payment_term_in_days"
              value={dialogData?.payment_term_in_days}
              placeholder="Payment Term (Days)"
              options={[30, 45, 60]}
              onChange={handleDialogDataChange}
            />
          </div>
          <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <SelectInput
              name="currency"
              value={dialogData?.currency}
              placeholder="Currency"
              options={['myr', 'usd']}
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="otc_amount"
              value={dialogData?.otc_amount}
              placeholder="OTC Amount"
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="otc_amortise_amount"
              value={dialogData?.otc_amortise_amount}
              placeholder="OTC (Amortise) Amount"
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="mrc_amount"
              value={dialogData?.mrc_amount}
              placeholder="MRC Amount"
              onChange={handleDialogDataChange}
            />
          </div>
        </div>
      </DialogContent>
      <DialogActions>
        <div className="flex w-full justify-between gap-4">
          <div className="flex gap-2">
            <button type="button" onClick={() => handleDialogAction(false)} className="p-2">
              Cancel
            </button>
            {editMode && (
              <button
                type="button"
                onClick={() => handleDialogAction('delete')}
                className="cta-btn bg-red-500"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex gap-2">
            {!editMode && (
              <button
                type="button"
                onClick={() => handleDialogAction('post')}
                className="bg-fa cta-btn"
              >
                Create
              </button>
            )}
            {editMode && (
              <button
                type="button"
                onClick={() => handleDialogAction('put')}
                className="bg-fa cta-btn"
              >
                Save
              </button>
            )}
          </div>
        </div>
      </DialogActions>
    </Dialog>
  );
};

OpexFormDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  editMode: PropTypes.bool.isRequired,
  dialogData: PropTypes.object,
  scenarioId: PropTypes.string,
  projectId: PropTypes.string,
  onSubmit: PropTypes.func.isRequired,
  projectData: PropTypes.object,
  scenarioData: PropTypes.object
};

export default OpexFormDialog;
