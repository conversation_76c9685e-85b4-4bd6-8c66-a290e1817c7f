import React from 'react';
import PropTypes from 'prop-types';
import { twMerge } from 'tailwind-merge';
import { checkAndReplaceNumberWithZero } from '../../../utils/shared';

const OpexTable = ({ opexData, isDisabled, onRowClick, scenarioData }) => {
  // Table styles
  const headerCellStyle =
  'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';

  return (
    <div className="overflow-x-auto mb-10">
              <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
        <thead>
          <tr>
            <td className={twMerge(headerCellStyle, 'w-[30px]')}>
              No.
            </td>
            {[
              { label: 'Provider', align: 'left' },
              { label: 'Details', align: 'left' },
              { label: 'Payment Term (Days)', align: 'right' },
              { label: 'Amount (OTC)', align: 'right' },
              { label: 'Amount (OTC Amortise)', align: 'right' },
              { label: 'Amount (MRC)', align: 'right' },
              { label: 'Start Date', align: 'center' },
              { label: 'End Date', align: 'center' },
            ].map((item, i) => (
              <td
                key={i}
                className={twMerge(
                  headerCellStyle, 
                  'w-[150px]',
                  item.align === 'right' ? 'text-right' : 
                  item.align === 'center' ? 'text-center' : ''
                )}
              >
                {item.label}
              </td>
            ))}
          </tr>
        </thead>
        <tbody>
          {opexData.map((row, i) => (
            <tr
              key={i}
              className="cursor-pointer hover:bg-gray-50 transition-colors duration-200"
              onClick={() => {
                if (isDisabled) return;
                onRowClick(row);
              }}
            >
              <td className={bodyCellStyle}>{i + 1}</td>
              <td className={bodyCellStyle}>{row?.provider}</td>
              <td className={bodyCellStyle}>{row?.details}</td>
              <td className={bodyCellStyle}>{row?.payment_term_in_days}</td>
              <td className={twMerge(bodyCellStyle, 'text-right font-mono')}>
                {row?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(row?.otc_amount).toLocaleString()}
              </td>
              <td className={twMerge(bodyCellStyle, 'text-right font-mono')}>
                {row?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(row?.otc_amortise_amount).toLocaleString()}
              </td>
              <td className={twMerge(bodyCellStyle, 'text-right font-mono')}>
                {row?.currency?.toUpperCase()} {checkAndReplaceNumberWithZero(row?.mrc_amount).toLocaleString()}
              </td>
              <td className={bodyCellStyle}>{row?.start_date || '-'}</td>
              <td className={bodyCellStyle}>{row?.end_date || '-'}</td>
            </tr>
          ))}
          <tr className="bg-blue-50 border-b border-blue-200">
            <td colSpan={4} className={twMerge(bodyCellStyle, 'font-bold text-right text-blue-900 border-b border-blue-200')}>
              Total
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200 text-right font-mono')}>
              USD {opexData?.reduce((prev, curr) => prev + curr?.otc_amount, 0).toLocaleString()}
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200 text-right font-mono')}>
              USD {opexData?.reduce((prev, curr) => prev + curr?.otc_amortise_amount, 0).toLocaleString()}
            </td>
            <td className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200 text-right font-mono')}>
              USD {opexData?.reduce((prev, curr) => prev + curr?.mrc_amount, 0).toLocaleString()}
            </td>
            <td colSpan={2} className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200')}></td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

OpexTable.propTypes = {
  opexData: PropTypes.array.isRequired,
  isDisabled: PropTypes.bool,
  onRowClick: PropTypes.func.isRequired,
  scenarioData: PropTypes.object,
};

export default OpexTable;
