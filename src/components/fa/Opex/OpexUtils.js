import moment from 'moment';

/**
 * Calculate amortization and MRC arrays based on contract data
 * @param {Object} data - The OPEX data with start/end dates and amounts
 * @param {Object} scenarioData - The scenario data containing contract period
 * @returns {Object} Object containing amortise and MRC arrays by year
 */
// Ensure data has all required numeric fields properly formatted
const sanitizeOpexData = (data) => {
  const result = { ...data };
  
  // Ensure all numeric fields are properly converted to numbers
  result.otc_amount = Number(data?.otc_amount || 0);
  result.otc_amortise_amount = Number(data?.otc_amortise_amount || 0);
  result.mrc_amount = Number(data?.mrc_amount || 0);
  
  // For backwards compatibility, if otc_amortise_amount is 0 but otc_amount is set,
  // use the same value for amortization (common in many OPEX entries)
  if (result.otc_amortise_amount === 0 && result.otc_amount > 0) {
    result.otc_amortise_amount = result.otc_amount;
    console.log('Auto-set otc_amortise_amount to match otc_amount:', result.otc_amount);
  }
  
  return result;
};

export const getMetrix = (data, scenarioData) => {
  // Sanitize the data to ensure all numeric fields are properly formatted
  const sanitizedData = sanitizeOpexData(data);
  
  // Debug the inputs to help identify issues
  console.log('getMetrix inputs:', {
    data_start_date: sanitizedData?.start_date,
    data_otc_amount: sanitizedData?.otc_amount,
    data_otc_amortise: sanitizedData?.otc_amortise_amount,
    data_mrc: sanitizedData?.mrc_amount,
    contract_period: scenarioData?.contract_period_in_month
  });

  const calculateAmortiseArray = () => {
    // Get contract info - ensure safe defaults
    const contractMonths = Number(scenarioData?.contract_period_in_month) || 120; // Default to 120 months if missing
    
    // Parse start date correctly
    const startDate = data?.start_date || '';
    let startMonth = 0;
    
    try {
      // Try to directly extract the month value from the date format
      if (startDate.includes('/')) {
        // For formats like DD/MM/YYYY or MM/DD/YYYY
        const parts = startDate.split('/');
        if (parts.length === 3) {
          if (parts[1].length <= 2 && parseInt(parts[1]) <= 12) {
            // Assuming it's DD/MM/YYYY format for "1/12/2024"
            startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
          }
        }
      } else if (startDate.includes('-')) {
        // For formats like YYYY-MM-DD
        const parts = startDate.split('-');
        if (parts.length === 3 && parts[0].length === 4) {
          // Standard ISO format YYYY-MM-DD
          startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
        }
      }
      
      // Fallback: try to use moment if direct parsing fails
      if (isNaN(startMonth)) {
        const m = moment(startDate, ["DD/MM/YYYY", "YYYY-MM-DD"]);
        startMonth = m.isValid() ? m.month() : 0;
      }
    } catch (error) {
      console.error("Date parsing error:", error);
      startMonth = 0; // Default to January
    }
    
    // Make sure we have a valid start month (0-11)
    startMonth = startMonth >= 0 && startMonth <= 11 ? startMonth : 0;
    
    console.log('Parsed start month:', startMonth, '(', ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][startMonth], ')');
    
    const totalAmount = sanitizedData?.otc_amortise_amount;

    // Calculate total years needed for display
    // Add the start month offset to contract months before dividing by 12
    // Calculate year gap between project start and RFS date with proper validation
    let yearDifference = 0;
    try {
      const projectStartMoment = moment(scenarioData?.project_start_date);
      const rfsMoment = moment(scenarioData?.expected_rfs_date);
      
      if (projectStartMoment.isValid() && rfsMoment.isValid()) {
        const startYear = projectStartMoment.year();
        const rfsYear = rfsMoment.year();
        yearDifference = Math.max(0, rfsYear - startYear);
      }
    } catch (error) {
      console.error("Year difference calculation error in MRC:", error);
      yearDifference = 0;
    }

    // Extend total years by the gap
    const contractYears = Math.ceil((contractMonths + startMonth) / 12);
    let totalYears = yearDifference + contractYears;

    // Validate the final result to ensure it's a valid array length
    if (!Number.isFinite(totalYears) || totalYears < 1 || totalYears > 1000) {
      console.warn('Invalid totalYears calculated in MRC:', totalYears, 'Defaulting to 10 years');
      totalYears = 10; // Default to a reasonable value
    }
    
    // Ensure it's an integer
    totalYears = Math.floor(totalYears);

    // Calculate months per year
    const remainingMonthsInFirstYear = 12 - startMonth;
    
    // Prevent division by zero
    const amountPerMonth = contractMonths > 0 ? (totalAmount / contractMonths) : 0;
    
    console.log('Amortization calculation details:', {
      totalYears,
      remainingMonthsInFirstYear,
      amountPerMonth,
      totalAmount,
      contractMonths
    });

    // Use the year gap calculated above

    // CAPEX-style: fill zeros for gap years, then fill values only from yearDifference onward
    return Array.from({ length: totalYears }, (_, index) =>
      index < yearDifference
        ? 0
        : (() => {
            let monthsInYear = 0;
            if (index === yearDifference) {
              // First active year: remaining months
              monthsInYear = Math.min(remainingMonthsInFirstYear, contractMonths);
            } else {
              // Subsequent years: either full 12 months or remaining contract months
              const monthsUsed = remainingMonthsInFirstYear + 12 * (index - yearDifference - 1);
              monthsInYear = Math.min(12, Math.max(0, contractMonths - monthsUsed));
            }
            return monthsInYear > 0 ? Math.round(amountPerMonth * monthsInYear) : 0;
          })()
    );
  };

  const calculateMrcArray = () => {
    // Get contract info - ensure safe defaults
    const contractMonths = Number(scenarioData?.contract_period_in_month) || 120; // Default to 120 months if missing
    
    // Parse start date correctly
    const startDate = data?.start_date || '';
    let startMonth = 0;
    
    try {
      // Try to directly extract the month value from the date format
      if (startDate.includes('/')) {
        // For formats like DD/MM/YYYY or MM/DD/YYYY
        const parts = startDate.split('/');
        if (parts.length === 3) {
          if (parts[1].length <= 2 && parseInt(parts[1]) <= 12) {
            // Assuming it's DD/MM/YYYY format for "1/12/2024"
            startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
          }
        }
      } else if (startDate.includes('-')) {
        // For formats like YYYY-MM-DD
        const parts = startDate.split('-');
        if (parts.length === 3 && parts[0].length === 4) {
          // Standard ISO format YYYY-MM-DD
          startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
        }
      }
      
      // Fallback: try to use moment if direct parsing fails
      if (isNaN(startMonth)) {
        const m = moment(startDate, ["DD/MM/YYYY", "YYYY-MM-DD"]);
        startMonth = m.isValid() ? m.month() : 0;
      }
    } catch (error) {
      console.error("Date parsing error:", error);
      startMonth = 0; // Default to January
    }
    
    // Make sure we have a valid start month (0-11)
    startMonth = startMonth >= 0 && startMonth <= 11 ? startMonth : 0;
    
    const monthlyAmount = sanitizedData?.mrc_amount;

    // Calculate total years needed for display
    // Add the start month offset to contract months before dividing by 12
    // Calculate year gap between project start and RFS date with proper validation
    let yearDifference = 0;
    try {
      const projectStartMoment = moment(scenarioData?.project_start_date);
      const rfsMoment = moment(scenarioData?.expected_rfs_date);
      
      if (projectStartMoment.isValid() && rfsMoment.isValid()) {
        const startYear = projectStartMoment.year();
        const rfsYear = rfsMoment.year();
        yearDifference = Math.max(0, rfsYear - startYear);
      }
    } catch (error) {
      console.error("Year difference calculation error in amortise:", error);
      yearDifference = 0;
    }

    // Extend total years by the gap
    const contractYears = Math.ceil((contractMonths + startMonth) / 12);
    let totalYears = yearDifference + contractYears;

    // Validate the final result to ensure it's a valid array length
    if (!Number.isFinite(totalYears) || totalYears < 1 || totalYears > 1000) {
      console.warn('Invalid totalYears calculated in amortise:', totalYears, 'Defaulting to 10 years');
      totalYears = 10; // Default to a reasonable value
    }
    
    // Ensure it's an integer
    totalYears = Math.floor(totalYears);

    // Calculate months per year
    const remainingMonthsInFirstYear = 12 - startMonth;
    
    console.log('MRC calculation details:', {
      totalYears,
      remainingMonthsInFirstYear,
      monthlyAmount,
      contractMonths
    });

    // Use the year gap calculated above

    // CAPEX-style: fill zeros for gap years, then fill values only from yearDifference onward
    return Array.from({ length: totalYears }, (_, index) =>
      index < yearDifference
        ? 0
        : (() => {
            let monthsInYear = 0;
            if (index === yearDifference) {
              // First active year: remaining months
              monthsInYear = Math.min(remainingMonthsInFirstYear, contractMonths);
            } else {
              // Subsequent years: either full 12 months or remaining contract months
              const monthsUsed = remainingMonthsInFirstYear + 12 * (index - yearDifference - 1);
              monthsInYear = Math.min(12, Math.max(0, contractMonths - monthsUsed));
            }
            return monthsInYear > 0 ? Math.round(monthlyAmount * monthsInYear) : 0;
          })()
    );
  };

  // Calculate both arrays
  const result = {
    amortiseArray: calculateAmortiseArray(),
    mrcArray: calculateMrcArray(),
  };
  
  console.log('getMetrix result:', result);
  
  return result;
};

/**
 * Calculate the total number of years needed for display based on contract data
 * @param {Object} data - The data containing start_date
 * @param {Object} scenarioData - The scenario data containing contract period
 * @returns {number} The total number of years
 */
export const calculateTotalYears = (data, scenarioData) => {
  // Parse start date correctly
  const startDate = data?.start_date || '';
  let startMonth = 0;
  
  try {
    // Try to directly extract the month value from the date format
    if (startDate.includes('/')) {
      // For formats like DD/MM/YYYY or MM/DD/YYYY
      const parts = startDate.split('/');
      if (parts.length === 3) {
        if (parts[1].length <= 2 && parseInt(parts[1]) <= 12) {
          // Assuming it's DD/MM/YYYY format for "1/12/2024"
          startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
        }
      }
    } else if (startDate.includes('-')) {
      // For formats like YYYY-MM-DD
      const parts = startDate.split('-');
      if (parts.length === 3 && parts[0].length === 4) {
        // Standard ISO format YYYY-MM-DD
        startMonth = parseInt(parts[1]) - 1; // Month is 0-indexed in JS
      }
    }
    
    // Fallback: try to use moment if direct parsing fails
    if (isNaN(startMonth)) {
      const m = moment(startDate, ["DD/MM/YYYY", "YYYY-MM-DD"]);
      startMonth = m.isValid() ? m.month() : 0;
    }
  } catch (error) {
    console.error("Date parsing error:", error);
    startMonth = 0; // Default to January
  }
  
  // Make sure we have a valid start month (0-11)
  startMonth = startMonth >= 0 && startMonth <= 11 ? startMonth : 0;
  
  const contractMonths = Number(scenarioData?.contract_period_in_month) || 120; // Default to 120 months if missing
  
  // Calculate year gap between project start and RFS date with proper validation
  let yearDifference = 0;
  try {
    const projectStartMoment = moment(scenarioData?.project_start_date);
    const rfsMoment = moment(scenarioData?.expected_rfs_date);
    
    if (projectStartMoment.isValid() && rfsMoment.isValid()) {
      const startYear = projectStartMoment.year();
      const rfsYear = rfsMoment.year();
      yearDifference = Math.max(0, rfsYear - startYear);
    }
  } catch (error) {
    console.error("Year difference calculation error:", error);
    yearDifference = 0;
  }
  
  // New formula to correctly calculate the total years needed
  // Add the start month offset to contract months before dividing by 12
  // This accounts for the "spillover" months at the end
  const contractYears = Math.ceil((contractMonths + startMonth) / 12);
  let totalYears = yearDifference + contractYears;
  
  // Validate the final result to ensure it's a valid array length
  if (!Number.isFinite(totalYears) || totalYears < 1 || totalYears > 1000) {
    console.warn('Invalid totalYears calculated:', totalYears, 'Defaulting to 10 years');
    totalYears = 10; // Default to a reasonable value
  }
  
  // Ensure it's an integer
  totalYears = Math.floor(totalYears);
  
  console.log('Total years calculation:', {
    startMonth,
    contractMonths,
    yearDifference,
    contractYears,
    totalYears
  });
  
  return totalYears;
};

/**
 * Get the formatted label for OPEX type
 * @param {string} paramType - The param_type value ('rou' or 'non-rou')
 * @returns {string} The formatted type label
 */
export const getOpexTypeLabel = (paramType) => {
  switch (paramType) {
    case 'rou':
      return 'ROU';
    case 'non-rou':
      return 'Non-ROU';
    default:
      return 'Non-ROU';
  }
};
