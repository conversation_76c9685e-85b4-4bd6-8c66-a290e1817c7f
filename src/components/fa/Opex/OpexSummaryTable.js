import React, { useEffect, Fragment } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { twMerge } from 'tailwind-merge';
import { checkAndReplaceNumberWithZero } from '../../../utils/shared';
import { getMetrix, calculateTotalYears, getOpexTypeLabel } from './OpexUtils';
import { getDisplayMode, generatePeriodLabels } from '../../../utils/faDisplayUtils';

// Ensure data has all required numeric fields properly formatted
const sanitizeOpexData = (data) => {
  const result = { ...data };

  // Ensure all numeric fields are properly converted to numbers
  result.otc_amount = Number(data?.otc_amount || 0);
  result.otc_amortise_amount = Number(data?.otc_amortise_amount || 0);
  result.mrc_amount = Number(data?.mrc_amount || 0);

  // For backwards compatibility, if otc_amortise_amount is 0 but otc_amount is set,
  // use the same value for amortization (common in many OPEX entries)
  if (result.otc_amortise_amount === 0 && result.otc_amount > 0) {
    result.otc_amortise_amount = result.otc_amount;
    console.log('Table: Auto-set otc_amortise_amount to match otc_amount:', result.otc_amount);
  }

  return result;
};

// Debug function to log important calculation values
const debugOpexData = (opexData, scenarioData) => {
  if (!opexData || opexData.length === 0) return;

  console.log('========== OPEX CALCULATION DEBUG ==========');

  // Sanitize the first row for debugging
  const firstRow = sanitizeOpexData(opexData[0]);
  console.log('First OPEX Entry (sanitized):', {
    provider: firstRow.provider,
    details: firstRow.details,
    start_date: firstRow.start_date,
    otc_amount: firstRow.otc_amount,
    otc_amortise_amount: firstRow.otc_amortise_amount,
    mrc_amount: firstRow.mrc_amount
  });

  console.log('Scenario Data:', {
    contract_period_in_month: scenarioData?.contract_period_in_month
  });

  // Debug date parsing
  const startDate = firstRow?.start_date || '';
  console.log('Date Parsing Debug:', {
    original_date: startDate,
    contains_slash: startDate.includes('/'),
    contains_dash: startDate.includes('-')
  });

  if (startDate.includes('/')) {
    const parts = startDate.split('/');
    console.log('Date Parts (if DD/MM/YYYY):', {
      day: parts[0],
      month: parts[1],
      year: parts[2],
      parsed_month_index: parseInt(parts[1]) - 1,
      month_name: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][parseInt(parts[1]) - 1]
    });
  }

  // Debug getMetrix results with sanitized data
  try {
    const metrix = getMetrix(sanitizeOpexData(firstRow), scenarioData);
    console.log('GetMetrix Results:', {
      amortiseArray: metrix.amortiseArray,
      mrcArray: metrix.mrcArray,
      amortiseTotal: metrix.amortiseArray.reduce((a, b) => a + b, 0),
      mrcTotal: metrix.mrcArray.reduce((a, b) => a + b, 0)
    });
  } catch (error) {
    console.error('Error in getMetrix:', error);
  }

  console.log('===========================================');
};

// Utility to format numbers with thousand separators and 0 decimals
const formatNumber = (num) => {
  if (isNaN(num)) return '-';
  return Number(num).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
};

// Simple OPEX Summary Table
const OpexSummaryTable = ({ opexData, scenarioData }) => {
  useEffect(() => {
    if (opexData && opexData.length > 0) {
      debugOpexData(opexData, scenarioData);
    }
  }, [opexData, scenarioData]);

  // Styles
  const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
  const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';
  const numberCellStyle = twMerge(bodyCellStyle, 'text-right font-mono');
  const numberCellStyleBold = twMerge(bodyCellStyle, 'text-right font-bold text-gray-800');
  const numberCellStyleTotal = twMerge(bodyCellStyle, 'bg-blue-50 text-right font-bold text-blue-900 border-b border-blue-200');

  if (!opexData || opexData.length === 0) {
    return (
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
          <tbody>
            <tr>
              <td className={bodyCellStyle}>No OPEX data available</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }

  // Determine display mode based on contract period (now using corrected getDisplayMode)
  const { isMonthlyDisplay, totalPeriods, contractMonths, startGapMonths } = getDisplayMode(scenarioData);

  // Calculate total years for yearly calculations (needed for data arrays)
  const startDate = opexData[0]?.start_date || '';
  let startMonth = 0;
  if (startDate.includes('/')) {
    const parts = startDate.split('/');
    if (parts.length === 3 && parts[1].length <= 2) {
      startMonth = parseInt(parts[1]) - 1;
    }
  } else if (startDate.includes('-')) {
    const parts = startDate.split('-');
    if (parts.length === 3 && parts[0].length === 4) {
      startMonth = parseInt(parts[1]) - 1;
    }
  }
  startMonth = startMonth >= 0 && startMonth <= 11 ? startMonth : 0;

  // Use calculateTotalYears to include the gap between project start and RFS date
  const totalYears = calculateTotalYears(opexData[0], scenarioData);

  // Log display mode for debugging
  console.log('OPEX Display mode (using corrected getDisplayMode):', {
    isMonthlyDisplay,
    totalPeriods,
    totalYears,
    contractMonths,
    startGapMonths,
    contract_period: scenarioData?.contract_period_in_month,
    expected_rfs_date: scenarioData?.expected_rfs_date
  });

  // Helper to get metrics for each entry
  const getMetrics = (entry) => getMetrix(entry, scenarioData);

  // Generate period labels (always Y0, Y1, etc.)
  const periodLabels = generatePeriodLabels(false, totalPeriods);

  // Grand totals - use totalPeriods from corrected getDisplayMode
  const grandOtc = Array(totalPeriods).fill(0);
  const grandAmortise = Array(totalPeriods).fill(0);
  const grandMrc = Array(totalPeriods).fill(0);

  return (
    <div className="overflow-x-auto scrollbar">
      <table className="min-w-full bg-white text-black">
        <tbody>
          {/* Header row */}
          <tr>
            <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Provider</td>
            <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Details</td>
            <td className={twMerge(headerCellStyle, 'w-[150px] overflow-hidden')}>Entry Type</td>
            {periodLabels.map((label, i) => (
              <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
            ))}
            <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden text-right')}>Total</td>
          </tr>

          {/* Loop through all OPEX entries */}
          {opexData.map((entry, idx) => {
            const sanitized = sanitizeOpexData(entry);
            const metrics = getMetrics(sanitized);

            // First calculate yearly arrays
            const yearlyAmortiseArray = Array(totalYears).fill(0).map((_, i) => metrics.amortiseArray[i] || 0);
            const yearlyMrcArray = Array(totalYears).fill(0).map((_, i) => metrics.mrcArray[i] || 0);
            const yearlyOtcRow = Array(totalYears).fill(0);
            yearlyOtcRow[0] = sanitized.otc_amount;

            // Always use yearly arrays but limit to totalPeriods
            const amortiseArray = yearlyAmortiseArray.slice(0, totalPeriods);
            const mrcArray = yearlyMrcArray.slice(0, totalPeriods);
            const otcRow = yearlyOtcRow.slice(0, totalPeriods);

            // Add to grand totals
            for (let i = 0; i < totalPeriods; i++) {
              grandOtc[i] += otcRow[i] || 0;
              grandAmortise[i] += amortiseArray[i] || 0;
              grandMrc[i] += mrcArray[i] || 0;
            }

            return (
              <Fragment key={idx}>
                {/* OTC Row */}
                <tr>
                  <td className={bodyCellStyle} rowSpan={3}>{sanitized.provider}</td>
                  <td className={bodyCellStyle} rowSpan={3}>{sanitized.details}</td>
                  <td className={bodyCellStyle}>OTC</td>
                  {otcRow.map((amount, i) => (
                    <td key={i} className={numberCellStyle}>{formatNumber(amount)}</td>
                  ))}
                  <td className={numberCellStyleTotal}>{formatNumber(sanitized.otc_amount)}</td>
                </tr>
                {/* OTC Amortise Row */}
                <tr>
                  <td className={bodyCellStyle}>OTC Amortise</td>
                  {amortiseArray.map((amount, i) => (
                    <td key={i} className={numberCellStyle}>{formatNumber(amount)}</td>
                  ))}
                  <td className={numberCellStyleTotal}>
                    {formatNumber(amortiseArray.reduce((a, b) => a + b, 0))}
                  </td>
                </tr>
                {/* ARC Row */}
                <tr>
                  <td className={bodyCellStyle}>ARC</td>
                  {mrcArray.map((amount, i) => (
                    <td key={i} className={numberCellStyle}>{formatNumber(amount)}</td>
                  ))}
                  <td className={numberCellStyleTotal}>
                    {formatNumber(mrcArray.reduce((a, b) => a + b, 0))}
                  </td>
                </tr>
                {/* Subtotal Row */}
                <tr className="bg-gray-50">
                  <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center font-semibold text-gray-800 border-b border-gray-200')}>Total</td>
                  {Array.from({ length: totalPeriods }, (_, i) => {
                    const periodTotal =
                      (otcRow[i] || 0) +
                      (amortiseArray[i] || 0) +
                      (mrcArray[i] || 0);
                    return (
                      <td key={i} className={numberCellStyleBold}>{formatNumber(periodTotal)}</td>
                    );
                  })}
                  <td className={numberCellStyleTotal}>
                    {formatNumber(sanitized.otc_amount +
                      amortiseArray.reduce((a, b) => a + b, 0) +
                      mrcArray.reduce((a, b) => a + b, 0))}
                  </td>
                </tr>
              </Fragment>
            );
          })}

          {/* Grand Total Row */}
          <tr className="bg-blue-50">
            <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center font-bold text-blue-900 border-b border-blue-200')}>Grand Total</td>
            {Array.from({ length: totalPeriods }, (_, i) => {
              const periodTotal =
                (grandOtc[i] || 0) +
                (grandAmortise[i] || 0) +
                (grandMrc[i] || 0);
              return (
                <td key={i} className={numberCellStyleBold}>{formatNumber(periodTotal)}</td>
              );
            })}
            <td className={numberCellStyleTotal}>
              {formatNumber(grandOtc.reduce((a, b) => a + b, 0) +
                grandAmortise.reduce((a, b) => a + b, 0) +
                grandMrc.reduce((a, b) => a + b, 0))}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

OpexSummaryTable.propTypes = {
  opexData: PropTypes.array.isRequired,
  scenarioData: PropTypes.object,
};

export default OpexSummaryTable;
